export interface NLPResult {
  intent: string
  entities: Record<string, any>
  formula?: string
  confidence: number
  explanation?: string
  variables?: string[]
  operation?: string
}

export interface MathPattern {
  pattern: RegExp
  intent: string
  formula: string
  variables?: string[]
  explanation: string
}

export class NLPProcessor {
  private mathPatterns: MathPattern[] = []
  private operationKeywords: Record<string, string[]> = {}
  private variablePatterns: RegExp[] = []

  constructor() {
    this.initializeMathPatterns()
    this.initializeOperationKeywords()
    this.initializeVariablePatterns()
  }

  /**
   * Process natural language input and convert to mathematical formula
   */
  async processNaturalLanguage(input: string): Promise<NLPResult> {
    const normalizedInput = this.normalizeInput(input)
    
    // Extract intent and entities
    const intent = this.extractIntent(normalizedInput)
    const entities = this.extractEntities(normalizedInput)
    const variables = this.extractVariables(normalizedInput)
    
    // Match against known patterns
    const patternMatch = this.matchPatterns(normalizedInput)
    
    if (patternMatch) {
      return {
        intent: patternMatch.intent,
        entities,
        formula: patternMatch.formula,
        confidence: 0.9,
        explanation: patternMatch.explanation,
        variables: patternMatch.variables || variables,
        operation: intent,
      }
    }

    // If no direct pattern match, try to construct formula from components
    const constructedFormula = this.constructFormula(intent, entities, variables)
    
    return {
      intent,
      entities,
      formula: constructedFormula.formula,
      confidence: constructedFormula.confidence,
      explanation: constructedFormula.explanation,
      variables,
      operation: intent,
    }
  }

  /**
   * Extract mathematical intent from natural language
   */
  private extractIntent(input: string): string {
    const intentPatterns = {
      solve: /solve|find|calculate|compute|determine/i,
      derivative: /derivative|differentiate|rate of change|slope/i,
      integral: /integrate|integral|area under|antiderivative/i,
      simplify: /simplify|reduce|minimize|optimize/i,
      graph: /graph|plot|visualize|chart/i,
      factor: /factor|factorize|decompose/i,
      expand: /expand|distribute|multiply out/i,
      limit: /limit|approaches|tends to/i,
      series: /series|sequence|sum|taylor|maclaurin/i,
      matrix: /matrix|determinant|eigenvalue|linear system/i,
      statistics: /mean|average|median|standard deviation|variance|correlation/i,
    }

    for (const [intent, pattern] of Object.entries(intentPatterns)) {
      if (pattern.test(input)) {
        return intent
      }
    }

    return 'evaluate'
  }

  /**
   * Extract mathematical entities (numbers, operations, functions)
   */
  private extractEntities(input: string): Record<string, any> {
    const entities: Record<string, any> = {}

    // Extract numbers
    const numbers = input.match(/-?\d+\.?\d*/g)
    if (numbers) {
      entities.numbers = numbers.map(n => parseFloat(n))
    }

    // Extract functions
    const functions = input.match(/\b(sin|cos|tan|log|ln|exp|sqrt|abs)\b/gi)
    if (functions) {
      entities.functions = functions.map(f => f.toLowerCase())
    }

    // Extract operations
    const operations = []
    if (/\b(plus|add|sum)\b/i.test(input)) operations.push('+')
    if (/\b(minus|subtract|difference)\b/i.test(input)) operations.push('-')
    if (/\b(times|multiply|product)\b/i.test(input)) operations.push('*')
    if (/\b(divide|quotient|ratio)\b/i.test(input)) operations.push('/')
    if (/\b(power|exponent|raised to)\b/i.test(input)) operations.push('^')
    if (/\b(square root|sqrt)\b/i.test(input)) operations.push('sqrt')

    if (operations.length > 0) {
      entities.operations = operations
    }

    return entities
  }

  /**
   * Extract variables from natural language
   */
  private extractVariables(input: string): string[] {
    const variables: string[] = []

    // Common variable patterns
    const variableMatches = input.match(/\b[a-z]\b/gi)
    if (variableMatches) {
      variables.push(...variableMatches.filter(v => 
        !['a', 'an', 'is', 'of', 'to', 'in', 'at', 'on'].includes(v.toLowerCase())
      ))
    }

    // Explicit variable mentions
    const explicitVars = input.match(/variable\s+([a-z])/gi)
    if (explicitVars) {
      variables.push(...explicitVars.map(v => v.split(' ')[1]))
    }

    return [...new Set(variables)] // Remove duplicates
  }

  /**
   * Match input against predefined mathematical patterns
   */
  private matchPatterns(input: string): MathPattern | null {
    for (const pattern of this.mathPatterns) {
      if (pattern.pattern.test(input)) {
        return pattern
      }
    }
    return null
  }

  /**
   * Construct formula from extracted components
   */
  private constructFormula(intent: string, entities: Record<string, any>, variables: string[]): {
    formula: string
    confidence: number
    explanation: string
  } {
    let formula = ''
    let confidence = 0.5
    let explanation = ''

    switch (intent) {
      case 'solve':
        if (entities.numbers && entities.operations) {
          formula = this.buildArithmeticExpression(entities.numbers, entities.operations)
          confidence = 0.8
          explanation = 'Constructed arithmetic expression from numbers and operations'
        } else if (variables.length > 0) {
          formula = `${variables[0]} = 0`
          confidence = 0.6
          explanation = `Assuming equation to solve for ${variables[0]}`
        }
        break

      case 'derivative':
        if (variables.length > 0) {
          const variable = variables[0]
          if (entities.functions && entities.functions.length > 0) {
            formula = `${entities.functions[0]}(${variable})`
            confidence = 0.8
            explanation = `Taking derivative of ${entities.functions[0]}(${variable})`
          } else {
            formula = variable
            confidence = 0.6
            explanation = `Taking derivative of ${variable}`
          }
        }
        break

      case 'integral':
        if (variables.length > 0) {
          const variable = variables[0]
          if (entities.functions && entities.functions.length > 0) {
            formula = `${entities.functions[0]}(${variable})`
            confidence = 0.8
            explanation = `Integrating ${entities.functions[0]}(${variable})`
          } else {
            formula = variable
            confidence = 0.6
            explanation = `Integrating ${variable}`
          }
        }
        break

      case 'statistics':
        if (entities.numbers) {
          formula = `[${entities.numbers.join(', ')}]`
          confidence = 0.9
          explanation = 'Statistical analysis of provided data'
        }
        break

      default:
        if (entities.numbers && entities.operations) {
          formula = this.buildArithmeticExpression(entities.numbers, entities.operations)
          confidence = 0.7
          explanation = 'Basic arithmetic expression'
        }
    }

    return { formula, confidence, explanation }
  }

  /**
   * Build arithmetic expression from numbers and operations
   */
  private buildArithmeticExpression(numbers: number[], operations: string[]): string {
    if (numbers.length === 0) return ''
    if (numbers.length === 1) return numbers[0].toString()

    let expression = numbers[0].toString()
    for (let i = 1; i < numbers.length && i - 1 < operations.length; i++) {
      expression += ` ${operations[i - 1]} ${numbers[i]}`
    }

    return expression
  }

  /**
   * Normalize input text for processing
   */
  private normalizeInput(input: string): string {
    return input
      .toLowerCase()
      .replace(/[^\w\s+\-*/().,=<>]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
  }

  /**
   * Initialize mathematical patterns for common expressions
   */
  private initializeMathPatterns(): void {
    this.mathPatterns = [
      // Quadratic formula
      {
        pattern: /quadratic formula|ax\^2\s*\+\s*bx\s*\+\s*c/i,
        intent: 'solve',
        formula: '(-b ± sqrt(b^2 - 4*a*c)) / (2*a)',
        variables: ['a', 'b', 'c', 'x'],
        explanation: 'Quadratic formula for solving ax² + bx + c = 0',
      },
      
      // Distance formula
      {
        pattern: /distance formula|distance between.*points/i,
        intent: 'solve',
        formula: 'sqrt((x2-x1)^2 + (y2-y1)^2)',
        variables: ['x1', 'y1', 'x2', 'y2'],
        explanation: 'Distance formula between two points',
      },
      
      // Area of circle
      {
        pattern: /area.*circle|circle.*area/i,
        intent: 'solve',
        formula: 'pi * r^2',
        variables: ['r'],
        explanation: 'Area of a circle with radius r',
      },
      
      // Pythagorean theorem
      {
        pattern: /pythagorean|a\^2\s*\+\s*b\^2\s*=\s*c\^2/i,
        intent: 'solve',
        formula: 'sqrt(a^2 + b^2)',
        variables: ['a', 'b'],
        explanation: 'Pythagorean theorem: c = √(a² + b²)',
      },
      
      // Slope formula
      {
        pattern: /slope|rise over run|(y2\s*-\s*y1).*(x2\s*-\s*x1)/i,
        intent: 'solve',
        formula: '(y2 - y1) / (x2 - x1)',
        variables: ['x1', 'y1', 'x2', 'y2'],
        explanation: 'Slope formula between two points',
      },
      
      // Compound interest
      {
        pattern: /compound interest|A\s*=\s*P.*\(1\s*\+\s*r\)/i,
        intent: 'solve',
        formula: 'P * (1 + r/n)^(n*t)',
        variables: ['P', 'r', 'n', 't'],
        explanation: 'Compound interest formula',
      },
    ]
  }

  /**
   * Initialize operation keywords mapping
   */
  private initializeOperationKeywords(): void {
    this.operationKeywords = {
      addition: ['add', 'plus', 'sum', 'total', 'combine'],
      subtraction: ['subtract', 'minus', 'difference', 'less'],
      multiplication: ['multiply', 'times', 'product', 'of'],
      division: ['divide', 'quotient', 'ratio', 'per'],
      exponentiation: ['power', 'exponent', 'raised to', 'squared', 'cubed'],
      root: ['root', 'square root', 'cube root', 'radical'],
    }
  }

  /**
   * Initialize variable detection patterns
   */
  private initializeVariablePatterns(): void {
    this.variablePatterns = [
      /\b[a-z]\b/g, // Single letters
      /variable\s+([a-z]+)/gi, // Explicit variable mentions
      /let\s+([a-z]+)/gi, // Variable declarations
    ]
  }
}
