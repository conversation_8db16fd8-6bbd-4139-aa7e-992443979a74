"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./components/analytics.tsx":
/*!**********************************!*\
  !*** ./components/analytics.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: function() { return /* binding */ Analytics; },\n/* harmony export */   analytics: function() { return /* binding */ analytics; },\n/* harmony export */   usePerformanceTracking: function() { return /* binding */ usePerformanceTracking; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics,analytics,usePerformanceTracking auto */ var _s = $RefreshSig$();\n\n\nfunction Analytics() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (true) return;\n        const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;\n        if (!GA_TRACKING_ID) return;\n        // Load Google Analytics script\n        const script = document.createElement(\"script\");\n        script.src = \"https://www.googletagmanager.com/gtag/js?id=\".concat(GA_TRACKING_ID);\n        script.async = true;\n        document.head.appendChild(script);\n        // Initialize gtag\n        window.gtag = window.gtag || function() {\n            window.dataLayer = window.dataLayer || [];\n            window.dataLayer.push(arguments);\n        };\n        window.gtag(\"js\", new Date().toISOString());\n        window.gtag(\"config\", GA_TRACKING_ID, {\n            page_title: document.title,\n            page_location: window.location.href\n        });\n        return ()=>{\n            document.head.removeChild(script);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (true) return;\n        if (!window.gtag) return;\n        const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;\n        if (!GA_TRACKING_ID) return;\n        const url = pathname + searchParams.toString();\n        window.gtag(\"config\", GA_TRACKING_ID, {\n            page_path: url,\n            page_title: document.title,\n            page_location: window.location.href\n        });\n    }, [\n        pathname,\n        searchParams\n    ]);\n    return null;\n}\n_s(Analytics, \"jq/6JV7jSw8H7h1siyRMT4JsAUQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams\n    ];\n});\n_c = Analytics;\n// Analytics utility functions\nconst analytics = {\n    // Track mathematical operations\n    trackFormulaGeneration: (data)=>{\n        if ( true && window.gtag) {\n            window.gtag(\"event\", \"formula_generation\", {\n                event_category: \"mathematical_operations\",\n                event_label: data.method,\n                value: data.confidence ? Math.round(data.confidence * 100) : undefined,\n                custom_parameters: {\n                    input_length: data.input.length,\n                    success: data.success\n                }\n            });\n        }\n    },\n    // Track equation solving\n    trackEquationSolving: (data)=>{\n        if ( true && window.gtag) {\n            window.gtag(\"event\", \"equation_solving\", {\n                event_category: \"mathematical_operations\",\n                event_label: data.method,\n                value: data.steps_count,\n                custom_parameters: {\n                    equation_length: data.equation.length,\n                    success: data.success\n                }\n            });\n        }\n    },\n    // Track visualization usage\n    trackVisualization: (data)=>{\n        if ( true && window.gtag) {\n            window.gtag(\"event\", \"visualization\", {\n                event_category: \"mathematical_operations\",\n                event_label: data.type,\n                custom_parameters: {\n                    function_complexity: data.function.length,\n                    success: data.success\n                }\n            });\n        }\n    },\n    // Track collaboration features\n    trackCollaboration: (data)=>{\n        if ( true && window.gtag) {\n            window.gtag(\"event\", \"collaboration\", {\n                event_category: \"user_interaction\",\n                event_label: data.action,\n                value: data.participants,\n                custom_parameters: {\n                    room_id: data.room_id\n                }\n            });\n        }\n    },\n    // Track user engagement\n    trackEngagement: (data)=>{\n        if ( true && window.gtag) {\n            window.gtag(\"event\", data.action, {\n                event_category: data.category,\n                value: data.value\n            });\n        }\n    },\n    // Track errors\n    trackError: (data)=>{\n        if ( true && window.gtag) {\n            window.gtag(\"event\", \"exception\", {\n                description: data.error_message,\n                fatal: false,\n                custom_parameters: {\n                    error_type: data.error_type,\n                    context: data.context\n                }\n            });\n        }\n    },\n    // Track performance metrics\n    trackPerformance: (data)=>{\n        if ( true && window.gtag) {\n            window.gtag(\"event\", \"timing_complete\", {\n                name: data.operation,\n                value: data.duration,\n                event_category: \"performance\",\n                custom_parameters: {\n                    success: data.success\n                }\n            });\n        }\n    },\n    // Track feature usage\n    trackFeatureUsage: (feature, details)=>{\n        if ( true && window.gtag) {\n            window.gtag(\"event\", \"feature_usage\", {\n                event_category: \"user_interaction\",\n                event_label: feature,\n                custom_parameters: details\n            });\n        }\n    }\n};\n// Performance monitoring hook\nfunction usePerformanceTracking() {\n    const trackOperation = (operation, fn)=>{\n        return async ()=>{\n            const startTime = performance.now();\n            let success = false;\n            try {\n                const result = await fn();\n                success = true;\n                return result;\n            } catch (error) {\n                analytics.trackError({\n                    error_type: \"computation_error\",\n                    error_message: error instanceof Error ? error.message : \"Unknown error\",\n                    context: operation\n                });\n                throw error;\n            } finally{\n                const duration = performance.now() - startTime;\n                analytics.trackPerformance({\n                    operation,\n                    duration,\n                    success\n                });\n            }\n        };\n    };\n    return {\n        trackOperation\n    };\n}\nvar _c;\n$RefreshReg$(_c, \"Analytics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/analytics.tsx\n"));

/***/ })

});