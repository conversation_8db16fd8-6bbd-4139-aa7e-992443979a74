'use client'

import React, { useRef, useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  BarChart3, 
  Download, 
  Settings, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw,
  Maximize,
  Play,
  Pause
} from 'lucide-react'

interface GraphRendererProps {
  formula?: string
  type?: '2d' | '3d' | 'parametric' | 'polar'
  width?: number
  height?: number
  animated?: boolean
  interactive?: boolean
  onExport?: (format: string) => void
}

interface GraphSettings {
  xMin: number
  xMax: number
  yMin: number
  yMax: number
  zMin?: number
  zMax?: number
  resolution: number
  showGrid: boolean
  showAxes: boolean
  colorScheme: string
}

export function GraphRenderer({
  formula = 'x^2',
  type = '2d',
  width = 600,
  height = 400,
  animated = false,
  interactive = true,
  onExport
}: GraphRendererProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [settings, setSettings] = useState<GraphSettings>({
    xMin: -10,
    xMax: 10,
    yMin: -10,
    yMax: 10,
    zMin: -10,
    zMax: 10,
    resolution: 100,
    showGrid: true,
    showAxes: true,
    colorScheme: 'blue'
  })
  const [zoom, setZoom] = useState(1)
  const [rotation, setRotation] = useState({ x: 0, y: 0 })

  // Initialize canvas and render graph
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    canvas.width = width
    canvas.height = height

    // Render based on type
    switch (type) {
      case '2d':
        render2DGraph(ctx, formula, settings)
        break
      case '3d':
        render3DGraph(ctx, formula, settings, rotation)
        break
      case 'parametric':
        renderParametricGraph(ctx, formula, settings)
        break
      case 'polar':
        renderPolarGraph(ctx, formula, settings)
        break
    }
  }, [formula, type, settings, zoom, rotation, width, height])

  // Animation loop
  useEffect(() => {
    if (!animated || !isPlaying) return

    const interval = setInterval(() => {
      setRotation(prev => ({
        x: prev.x + 0.01,
        y: prev.y + 0.005
      }))
    }, 50)

    return () => clearInterval(interval)
  }, [animated, isPlaying])

  const render2DGraph = (ctx: CanvasRenderingContext2D, formula: string, settings: GraphSettings) => {
    const { xMin, xMax, yMin, yMax, resolution, showGrid, showAxes } = settings
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height)
    
    // Set up coordinate system
    const scaleX = width / (xMax - xMin)
    const scaleY = height / (yMax - yMin)
    
    // Draw grid
    if (showGrid) {
      ctx.strokeStyle = '#e5e7eb'
      ctx.lineWidth = 0.5
      
      // Vertical lines
      for (let x = Math.ceil(xMin); x <= xMax; x++) {
        const canvasX = (x - xMin) * scaleX
        ctx.beginPath()
        ctx.moveTo(canvasX, 0)
        ctx.lineTo(canvasX, height)
        ctx.stroke()
      }
      
      // Horizontal lines
      for (let y = Math.ceil(yMin); y <= yMax; y++) {
        const canvasY = height - (y - yMin) * scaleY
        ctx.beginPath()
        ctx.moveTo(0, canvasY)
        ctx.lineTo(width, canvasY)
        ctx.stroke()
      }
    }
    
    // Draw axes
    if (showAxes) {
      ctx.strokeStyle = '#374151'
      ctx.lineWidth = 2
      
      // X-axis
      if (yMin <= 0 && yMax >= 0) {
        const y0 = height - (0 - yMin) * scaleY
        ctx.beginPath()
        ctx.moveTo(0, y0)
        ctx.lineTo(width, y0)
        ctx.stroke()
      }
      
      // Y-axis
      if (xMin <= 0 && xMax >= 0) {
        const x0 = (0 - xMin) * scaleX
        ctx.beginPath()
        ctx.moveTo(x0, 0)
        ctx.lineTo(x0, height)
        ctx.stroke()
      }
    }
    
    // Plot function
    ctx.strokeStyle = getColorScheme(settings.colorScheme)
    ctx.lineWidth = 3
    ctx.beginPath()
    
    let firstPoint = true
    for (let i = 0; i <= resolution; i++) {
      const x = xMin + (i / resolution) * (xMax - xMin)
      const y = evaluateFormula(formula, x)
      
      if (isFinite(y) && y >= yMin && y <= yMax) {
        const canvasX = (x - xMin) * scaleX
        const canvasY = height - (y - yMin) * scaleY
        
        if (firstPoint) {
          ctx.moveTo(canvasX, canvasY)
          firstPoint = false
        } else {
          ctx.lineTo(canvasX, canvasY)
        }
      } else {
        firstPoint = true
      }
    }
    
    ctx.stroke()
  }

  const render3DGraph = (ctx: CanvasRenderingContext2D, formula: string, settings: GraphSettings, rotation: { x: number, y: number }) => {
    // Simplified 3D rendering - in a real implementation, you'd use Three.js or WebGL
    ctx.clearRect(0, 0, width, height)
    
    // Draw 3D wireframe representation
    ctx.strokeStyle = getColorScheme(settings.colorScheme)
    ctx.lineWidth = 1
    
    const centerX = width / 2
    const centerY = height / 2
    const scale = 100
    
    // Draw a simple 3D surface representation
    for (let i = -5; i <= 5; i++) {
      for (let j = -5; j <= 5; j++) {
        const x = i
        const y = j
        const z = evaluateFormula3D(formula, x, y)
        
        // Simple 3D projection
        const projX = centerX + (x * Math.cos(rotation.y) - z * Math.sin(rotation.y)) * scale
        const projY = centerY + (y * Math.cos(rotation.x) + z * Math.sin(rotation.x)) * scale
        
        ctx.fillStyle = getColorScheme(settings.colorScheme)
        ctx.fillRect(projX - 2, projY - 2, 4, 4)
      }
    }
  }

  const renderParametricGraph = (ctx: CanvasRenderingContext2D, formula: string, settings: GraphSettings) => {
    // Parametric curve rendering
    ctx.clearRect(0, 0, width, height)
    ctx.strokeStyle = getColorScheme(settings.colorScheme)
    ctx.lineWidth = 2
    ctx.beginPath()
    
    const centerX = width / 2
    const centerY = height / 2
    const scale = 50
    
    for (let t = 0; t <= 2 * Math.PI; t += 0.01) {
      const x = Math.cos(t) * scale
      const y = Math.sin(t) * scale
      
      if (t === 0) {
        ctx.moveTo(centerX + x, centerY + y)
      } else {
        ctx.lineTo(centerX + x, centerY + y)
      }
    }
    
    ctx.stroke()
  }

  const renderPolarGraph = (ctx: CanvasRenderingContext2D, formula: string, settings: GraphSettings) => {
    // Polar coordinate rendering
    ctx.clearRect(0, 0, width, height)
    ctx.strokeStyle = getColorScheme(settings.colorScheme)
    ctx.lineWidth = 2
    ctx.beginPath()
    
    const centerX = width / 2
    const centerY = height / 2
    const scale = 50
    
    for (let theta = 0; theta <= 2 * Math.PI; theta += 0.01) {
      const r = evaluateFormula(formula, theta)
      const x = r * Math.cos(theta) * scale
      const y = r * Math.sin(theta) * scale
      
      if (theta === 0) {
        ctx.moveTo(centerX + x, centerY + y)
      } else {
        ctx.lineTo(centerX + x, centerY + y)
      }
    }
    
    ctx.stroke()
  }

  const evaluateFormula = (formula: string, x: number): number => {
    try {
      // Simple formula evaluation - in production, use a proper math parser
      const expr = formula
        .replace(/x/g, x.toString())
        .replace(/\^/g, '**')
        .replace(/sin/g, 'Math.sin')
        .replace(/cos/g, 'Math.cos')
        .replace(/tan/g, 'Math.tan')
        .replace(/log/g, 'Math.log')
        .replace(/sqrt/g, 'Math.sqrt')
      
      return eval(expr)
    } catch {
      return NaN
    }
  }

  const evaluateFormula3D = (formula: string, x: number, y: number): number => {
    try {
      // Simple 3D formula evaluation
      const expr = formula
        .replace(/x/g, x.toString())
        .replace(/y/g, y.toString())
        .replace(/\^/g, '**')
      
      return eval(expr) || 0
    } catch {
      return 0
    }
  }

  const getColorScheme = (scheme: string): string => {
    const schemes = {
      blue: '#3b82f6',
      green: '#10b981',
      purple: '#8b5cf6',
      red: '#ef4444',
      orange: '#f97316'
    }
    return schemes[scheme as keyof typeof schemes] || schemes.blue
  }

  const handleZoomIn = () => setZoom(prev => Math.min(prev * 1.2, 5))
  const handleZoomOut = () => setZoom(prev => Math.max(prev / 1.2, 0.2))
  const handleReset = () => {
    setZoom(1)
    setRotation({ x: 0, y: 0 })
  }

  const handleExport = (format: string) => {
    const canvas = canvasRef.current
    if (!canvas) return

    if (format === 'png') {
      const link = document.createElement('a')
      link.download = 'graph.png'
      link.href = canvas.toDataURL()
      link.click()
    }
    
    onExport?.(format)
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-primary" />
            <CardTitle>Graph Renderer</CardTitle>
            <Badge variant="outline">{type.toUpperCase()}</Badge>
          </div>
          
          <div className="flex items-center gap-2">
            {animated && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsPlaying(!isPlaying)}
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </Button>
            )}
            
            <Button size="sm" variant="outline" onClick={handleZoomIn}>
              <ZoomIn className="w-4 h-4" />
            </Button>
            
            <Button size="sm" variant="outline" onClick={handleZoomOut}>
              <ZoomOut className="w-4 h-4" />
            </Button>
            
            <Button size="sm" variant="outline" onClick={handleReset}>
              <RotateCcw className="w-4 h-4" />
            </Button>
            
            <Button size="sm" variant="outline" onClick={() => handleExport('png')}>
              <Download className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Canvas */}
        <div className="relative border rounded-lg overflow-hidden bg-white dark:bg-gray-900">
          <canvas
            ref={canvasRef}
            className="w-full h-auto"
            style={{ transform: `scale(${zoom})`, transformOrigin: 'center' }}
          />
        </div>
        
        {/* Controls */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <Label htmlFor="xMin">X Min</Label>
            <Input
              id="xMin"
              type="number"
              value={settings.xMin}
              onChange={(e) => setSettings(prev => ({ ...prev, xMin: Number(e.target.value) }))}
            />
          </div>
          
          <div>
            <Label htmlFor="xMax">X Max</Label>
            <Input
              id="xMax"
              type="number"
              value={settings.xMax}
              onChange={(e) => setSettings(prev => ({ ...prev, xMax: Number(e.target.value) }))}
            />
          </div>
          
          <div>
            <Label htmlFor="resolution">Resolution</Label>
            <Input
              id="resolution"
              type="number"
              value={settings.resolution}
              onChange={(e) => setSettings(prev => ({ ...prev, resolution: Number(e.target.value) }))}
            />
          </div>
          
          <div>
            <Label htmlFor="colorScheme">Color</Label>
            <Select
              value={settings.colorScheme}
              onValueChange={(value) => setSettings(prev => ({ ...prev, colorScheme: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="blue">Blue</SelectItem>
                <SelectItem value="green">Green</SelectItem>
                <SelectItem value="purple">Purple</SelectItem>
                <SelectItem value="red">Red</SelectItem>
                <SelectItem value="orange">Orange</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
