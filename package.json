{"name": "formulaforge", "version": "1.0.0", "description": "The Ultimate Mathematical Formula Generation and Manipulation Platform", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "analyze": "cross-env ANALYZE=true next build"}, "keywords": ["mathematics", "formulas", "equations", "calculator", "symbolic-math", "ai", "nlp", "visualization"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/FormulaForge.git"}, "dependencies": {"@next/bundle-analyzer": "^14.0.4", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-card": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-textarea": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.38.5", "@tensorflow/tfjs": "^4.15.0", "@tensorflow/tfjs-node": "^4.15.0", "@types/d3": "^7.4.3", "@types/katex": "^0.16.7", "@types/mathjs": "^9.4.2", "@types/three": "^0.158.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "d3": "^7.8.5", "framer-motion": "^10.16.16", "katex": "^0.16.9", "lucide-react": "^0.294.0", "mathjs": "^12.2.1", "ml-matrix": "^6.10.7", "next": "^14.0.4", "next-themes": "^0.2.1", "openai": "^4.20.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-katex": "^3.0.1", "react-three-fiber": "^6.0.13", "recharts": "^2.8.0", "simple-statistics": "^7.8.3", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "three": "^0.158.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}