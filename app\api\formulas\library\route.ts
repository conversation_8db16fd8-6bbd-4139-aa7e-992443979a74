import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { rateLimit } from '@/lib/rate-limit'

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Rate limiting
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500,
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 50, 'CACHE_TOKEN') // 50 formula saves per minute
    } catch {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { 
      name, 
      description, 
      formula, 
      category, 
      tags = [], 
      isPublic = false,
      variables = [],
      examples = []
    } = body

    // Validate input
    if (!name || !formula) {
      return NextResponse.json(
        { error: 'Name and formula are required' },
        { status: 400 }
      )
    }

    if (name.length < 3 || name.length > 100) {
      return NextResponse.json(
        { error: 'Name must be between 3 and 100 characters' },
        { status: 400 }
      )
    }

    // Get user from authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authorization' },
        { status: 401 }
      )
    }

    // Generate formula ID
    const formulaId = `formula_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Save formula to database
    const { data: savedFormula, error: saveError } = await supabase
      .from('formula_library')
      .insert({
        id: formulaId,
        name,
        description,
        formula,
        category,
        tags,
        is_public: isPublic,
        variables,
        examples,
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        usage_count: 0,
        rating: 0,
        rating_count: 0,
      })
      .select()
      .single()

    if (saveError) {
      console.error('Formula save error:', saveError)
      return NextResponse.json(
        { error: 'Failed to save formula' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      formula: {
        id: formulaId,
        name,
        description,
        formula,
        category,
        tags,
        isPublic,
        variables,
        examples,
        createdBy: user.id,
        createdAt: savedFormula.created_at,
        usageCount: 0,
        rating: 0,
      },
    })

  } catch (error) {
    console.error('Formula save error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 200, 'CACHE_TOKEN') // 200 requests per minute
    } catch {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 50)
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const tags = searchParams.get('tags')?.split(',').filter(Boolean)
    const isPublic = searchParams.get('public') === 'true'
    const sortBy = searchParams.get('sortBy') || 'created_at'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Get user for private formulas
    let userId: string | null = null
    const authHeader = request.headers.get('authorization')
    if (authHeader) {
      const token = authHeader.replace('Bearer ', '')
      const { data: { user } } = await supabase.auth.getUser(token)
      userId = user?.id || null
    }

    let query = supabase
      .from('formula_library')
      .select(`
        *,
        profiles:created_by(email, full_name)
      `)

    // Filter by public formulas or user's own formulas
    if (isPublic) {
      query = query.eq('is_public', true)
    } else if (userId) {
      query = query.or(`is_public.eq.true,created_by.eq.${userId}`)
    } else {
      query = query.eq('is_public', true)
    }

    // Category filter
    if (category) {
      query = query.eq('category', category)
    }

    // Search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,formula.ilike.%${search}%`)
    }

    // Tags filter
    if (tags && tags.length > 0) {
      query = query.overlaps('tags', tags)
    }

    // Sorting
    const validSortFields = ['created_at', 'updated_at', 'name', 'usage_count', 'rating']
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at'
    const ascending = sortOrder === 'asc'

    query = query.order(sortField, { ascending })

    // Pagination
    const from = (page - 1) * limit
    const to = from + limit - 1

    const { data: formulas, error, count } = await query.range(from, to)

    if (error) {
      console.error('Formulas fetch error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch formulas' },
        { status: 500 }
      )
    }

    // Format response
    const formattedFormulas = formulas?.map(formula => ({
      id: formula.id,
      name: formula.name,
      description: formula.description,
      formula: formula.formula,
      category: formula.category,
      tags: formula.tags,
      isPublic: formula.is_public,
      variables: formula.variables,
      examples: formula.examples,
      createdBy: {
        id: formula.created_by,
        email: formula.profiles?.email,
        name: formula.profiles?.full_name,
      },
      createdAt: formula.created_at,
      updatedAt: formula.updated_at,
      usageCount: formula.usage_count,
      rating: formula.rating,
      ratingCount: formula.rating_count,
    })) || []

    return NextResponse.json({
      success: true,
      formulas: formattedFormulas,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    })

  } catch (error) {
    console.error('Formulas fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 30, 'CACHE_TOKEN') // 30 updates per minute
    } catch {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { 
      id,
      name, 
      description, 
      formula, 
      category, 
      tags, 
      isPublic,
      variables,
      examples
    } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Formula ID is required' },
        { status: 400 }
      )
    }

    // Get user from authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authorization' },
        { status: 401 }
      )
    }

    // Check if user owns the formula
    const { data: existingFormula, error: fetchError } = await supabase
      .from('formula_library')
      .select('created_by')
      .eq('id', id)
      .single()

    if (fetchError || !existingFormula) {
      return NextResponse.json(
        { error: 'Formula not found' },
        { status: 404 }
      )
    }

    if (existingFormula.created_by !== user.id) {
      return NextResponse.json(
        { error: 'You can only update your own formulas' },
        { status: 403 }
      )
    }

    // Update formula
    const updateData: any = {
      updated_at: new Date().toISOString(),
    }

    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (formula !== undefined) updateData.formula = formula
    if (category !== undefined) updateData.category = category
    if (tags !== undefined) updateData.tags = tags
    if (isPublic !== undefined) updateData.is_public = isPublic
    if (variables !== undefined) updateData.variables = variables
    if (examples !== undefined) updateData.examples = examples

    const { data: updatedFormula, error: updateError } = await supabase
      .from('formula_library')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (updateError) {
      console.error('Formula update error:', updateError)
      return NextResponse.json(
        { error: 'Failed to update formula' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      formula: updatedFormula,
    })

  } catch (error) {
    console.error('Formula update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 20, 'CACHE_TOKEN') // 20 deletions per minute
    } catch {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const formulaId = searchParams.get('id')

    if (!formulaId) {
      return NextResponse.json(
        { error: 'Formula ID is required' },
        { status: 400 }
      )
    }

    // Get user from authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authorization' },
        { status: 401 }
      )
    }

    // Check if user owns the formula
    const { data: formula, error: fetchError } = await supabase
      .from('formula_library')
      .select('created_by')
      .eq('id', formulaId)
      .single()

    if (fetchError || !formula) {
      return NextResponse.json(
        { error: 'Formula not found' },
        { status: 404 }
      )
    }

    if (formula.created_by !== user.id) {
      return NextResponse.json(
        { error: 'You can only delete your own formulas' },
        { status: 403 }
      )
    }

    // Delete formula
    const { error: deleteError } = await supabase
      .from('formula_library')
      .delete()
      .eq('id', formulaId)

    if (deleteError) {
      console.error('Formula deletion error:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete formula' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Formula deleted successfully',
    })

  } catch (error) {
    console.error('Formula deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
