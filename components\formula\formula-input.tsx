'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Calculator, 
  Brain, 
  Zap, 
  Copy, 
  Download, 
  Share2,
  Lightbulb,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { cn, debounce } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'

interface FormulaInputProps {
  onFormulaChange?: (formula: string) => void
  onNLPResult?: (result: any) => void
  onEvaluationResult?: (result: any) => void
  className?: string
  placeholder?: string
  defaultValue?: string
}

interface ProcessingState {
  isProcessing: boolean
  type: 'nlp' | 'evaluation' | null
  progress: number
}

export function FormulaInput({
  onFormulaChange,
  onNLPResult,
  onEvaluationResult,
  className,
  placeholder = "Enter a mathematical expression or describe what you want to calculate...",
  defaultValue = ""
}: FormulaInputProps) {
  const [input, setInput] = useState(defaultValue)
  const [activeTab, setActiveTab] = useState<'natural' | 'formula'>('natural')
  const [processingState, setProcessingState] = useState<ProcessingState>({
    isProcessing: false,
    type: null,
    progress: 0
  })
  const [nlpResult, setNlpResult] = useState<any>(null)
  const [evaluationResult, setEvaluationResult] = useState<any>(null)
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [error, setError] = useState<string | null>(null)
  
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const { toast } = useToast()

  // Debounced processing function
  const debouncedProcess = useCallback(
    debounce(async (value: string, type: 'nlp' | 'formula') => {
      if (!value.trim()) {
        setNlpResult(null)
        setEvaluationResult(null)
        setSuggestions([])
        return
      }

      setProcessingState({ isProcessing: true, type: type === 'nlp' ? 'nlp' : 'evaluation', progress: 0 })
      setError(null)

      try {
        if (type === 'nlp') {
          await processNaturalLanguage(value)
        } else {
          await evaluateFormula(value)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Processing failed')
      } finally {
        setProcessingState({ isProcessing: false, type: null, progress: 100 })
      }
    }, 500),
    []
  )

  // Process natural language input
  const processNaturalLanguage = async (input: string) => {
    const response = await fetch('/api/nlp/process', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        input, 
        useAI: true,
        context: { previousResults: nlpResult }
      }),
    })

    if (!response.ok) {
      throw new Error('Failed to process natural language')
    }

    const data = await response.json()
    setNlpResult(data.nlp)
    setEvaluationResult(data.evaluation)
    setSuggestions(data.suggestions || [])
    
    onNLPResult?.(data.nlp)
    onEvaluationResult?.(data.evaluation)
  }

  // Evaluate mathematical formula
  const evaluateFormula = async (formula: string) => {
    const response = await fetch('/api/math/evaluate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        expression: formula,
        operation: 'evaluate'
      }),
    })

    if (!response.ok) {
      throw new Error('Failed to evaluate formula')
    }

    const data = await response.json()
    setEvaluationResult(data.result)
    onEvaluationResult?.(data.result)
  }

  // Handle input change
  const handleInputChange = (value: string) => {
    setInput(value)
    onFormulaChange?.(value)
    
    if (activeTab === 'natural') {
      debouncedProcess(value, 'nlp')
    } else {
      debouncedProcess(value, 'formula')
    }
  }

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab as 'natural' | 'formula')
    if (input.trim()) {
      debouncedProcess(input, tab as 'nlp' | 'formula')
    }
  }

  // Copy result to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied to clipboard",
        description: "The result has been copied to your clipboard.",
      })
    } catch (err) {
      toast({
        title: "Copy failed",
        description: "Failed to copy to clipboard.",
        variant: "destructive",
      })
    }
  }

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [input])

  return (
    <div className={cn("w-full max-w-4xl mx-auto space-y-6", className)}>
      {/* Input Section */}
      <Card className="relative overflow-hidden">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5 text-primary" />
            Formula Input
            {processingState.isProcessing && (
              <Badge variant="secondary" className="animate-pulse">
                Processing...
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="natural" className="flex items-center gap-2">
                <Brain className="w-4 h-4" />
                Natural Language
              </TabsTrigger>
              <TabsTrigger value="formula" className="flex items-center gap-2">
                <Calculator className="w-4 h-4" />
                Mathematical Formula
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="natural" className="mt-4">
              <Textarea
                ref={textareaRef}
                value={input}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder="Describe what you want to calculate (e.g., 'find the derivative of x squared' or 'solve 2x + 5 = 15')"
                className="min-h-[100px] resize-none math-input"
                disabled={processingState.isProcessing}
              />
            </TabsContent>
            
            <TabsContent value="formula" className="mt-4">
              <Textarea
                ref={textareaRef}
                value={input}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder="Enter mathematical expression (e.g., '2*x + 5 = 15' or 'derivative(x^2, x)')"
                className="min-h-[100px] resize-none math-input font-mono"
                disabled={processingState.isProcessing}
              />
            </TabsContent>
          </Tabs>

          {/* Processing indicator */}
          {processingState.isProcessing && (
            <div className="mt-4 flex items-center gap-2 text-sm text-muted-foreground">
              <div className="animate-spin w-4 h-4 border-2 border-primary border-t-transparent rounded-full" />
              {processingState.type === 'nlp' ? 'Processing natural language...' : 'Evaluating formula...'}
            </div>
          )}

          {/* Error display */}
          {error && (
            <div className="mt-4 flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <AlertCircle className="w-4 h-4 text-destructive" />
              <span className="text-sm text-destructive">{error}</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* NLP Result */}
      {nlpResult && (
        <Card className="formula-container">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-formula-500" />
                Interpreted Formula
              </span>
              <Badge variant={nlpResult.confidence > 0.8 ? "default" : "secondary"}>
                {Math.round(nlpResult.confidence * 100)}% confidence
              </Badge>
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {nlpResult.formula && (
              <div className="p-4 bg-muted/30 rounded-md font-mono text-center">
                {nlpResult.formula}
              </div>
            )}
            
            {nlpResult.explanation && (
              <p className="text-sm text-muted-foreground">
                {nlpResult.explanation}
              </p>
            )}
            
            <div className="flex flex-wrap gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => copyToClipboard(nlpResult.formula)}
                className="flex items-center gap-1"
              >
                <Copy className="w-3 h-3" />
                Copy Formula
              </Button>
              
              {nlpResult.variables && nlpResult.variables.length > 0 && (
                <div className="flex items-center gap-1">
                  <span className="text-xs text-muted-foreground">Variables:</span>
                  {nlpResult.variables.map((variable: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {variable}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Evaluation Result */}
      {evaluationResult && (
        <Card className="math-container">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-math-500" />
              Result
            </CardTitle>
          </CardHeader>
          
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-math-50 dark:bg-math-950/20 rounded-md text-center">
                <div className="text-2xl font-bold text-math-700 dark:text-math-300">
                  {typeof evaluationResult.result === 'number' 
                    ? evaluationResult.result.toLocaleString()
                    : String(evaluationResult.result)
                  }
                </div>
              </div>
              
              {evaluationResult.steps && evaluationResult.steps.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Step-by-step solution:</h4>
                  <div className="space-y-1">
                    {evaluationResult.steps.map((step: string, index: number) => (
                      <div key={index} className="solution-step text-sm">
                        {step}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Suggestions */}
      {suggestions.length > 0 && (
        <Card className="border-yellow-200 dark:border-yellow-800">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-yellow-700 dark:text-yellow-300">
              <Lightbulb className="w-5 h-5" />
              Suggestions
            </CardTitle>
          </CardHeader>
          
          <CardContent>
            <ul className="space-y-2">
              {suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <span className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
                  {suggestion}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
