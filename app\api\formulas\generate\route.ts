import { NextRequest, NextResponse } from 'next/server'
import { FormulaGenerator, FormulaGenerationRequest } from '@/backend/ai/formula-generator'
import { apiLogger, getRequestContext, FormulaError, ValidationError } from '@/backend/utils/logger'
import { performanceMonitor } from '@/backend/utils/performance'

// Rate limiting (in production, use Redis or similar)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

function getRateLimitKey(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : 'unknown'
  return ip
}

function checkRateLimit(key: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now()
  const record = rateLimitMap.get(key)
  
  if (!record || now > record.resetTime) {
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (record.count >= limit) {
    return false
  }
  
  record.count++
  return true
}

export async function POST(request: NextRequest) {
  const timer = performanceMonitor.startTimer('api.formulas.generate')
  const { requestId, userId, ip } = getRequestContext(request)

  try {
    apiLogger.info('Formula generation request started', { endpoint: '/api/formulas/generate' }, requestId, userId)

    // Rate limiting
    const rateLimitKey = getRateLimitKey(request)
    if (!checkRateLimit(rateLimitKey)) {
      apiLogger.warn('Rate limit exceeded', { ip, endpoint: '/api/formulas/generate' }, requestId, userId)
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { naturalLanguage, context, complexity, variables, constraints }: FormulaGenerationRequest = body

    // Validation
    if (!naturalLanguage || typeof naturalLanguage !== 'string') {
      throw new ValidationError('Natural language description is required', 'naturalLanguage', requestId)
    }

    if (naturalLanguage.length > 1000) {
      throw new ValidationError('Description too long. Please keep it under 1000 characters.', 'naturalLanguage', requestId)
    }

    // Generate formula
    timer.addMetadata('naturalLanguage_length', naturalLanguage.length)
    timer.addMetadata('context', context)
    timer.addMetadata('complexity', complexity)

    const generator = new FormulaGenerator()
    const result = await generator.generateFormula({
      naturalLanguage,
      context,
      complexity,
      variables,
      constraints
    })

    // Log successful generation
    apiLogger.info('Formula generated successfully', {
      naturalLanguage: naturalLanguage.substring(0, 50) + '...',
      confidence: result.confidence,
      context,
      complexity
    }, requestId, userId)

    timer.addMetadata('confidence', result.confidence)
    timer.addMetadata('alternatives_count', result.alternatives.length)
    const duration = timer.end()

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      performance: { duration }
    })

  } catch (error) {
    timer.addMetadata('error', true)
    timer.end()

    if (error instanceof ValidationError || error instanceof FormulaError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      )
    }

    apiLogger.error('Formula generation failed', error as Error, {
      endpoint: '/api/formulas/generate'
    }, requestId, userId, ip)

    return NextResponse.json(
      {
        error: 'Internal server error. Please try again later.',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Formula Generation API',
    version: '1.0.0',
    endpoints: {
      POST: '/api/formulas/generate - Generate formula from natural language',
    },
    rateLimit: {
      requests: 10,
      window: '1 minute'
    }
  })
}
