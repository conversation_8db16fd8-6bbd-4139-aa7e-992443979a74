import { Metadata } from 'next'
import { FormulaInput } from '@/components/formula/formula-input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Calculator, Brain, Zap, BarChart3 } from 'lucide-react'

export const metadata: Metadata = {
  title: 'FormulaForge App - Mathematical Formula Generator',
  description: 'Generate and manipulate mathematical formulas with AI-powered natural language processing.',
}

export default function AppPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/80 backdrop-blur-sm sticky top-0 z-40">
        <div className="container-wide py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/60 rounded-lg flex items-center justify-center">
                <Calculator className="w-4 h-4 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold">FormulaForge</h1>
                <p className="text-xs text-muted-foreground">Mathematical Formula Generator</p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="hidden sm:flex">
                <Brain className="w-3 h-3 mr-1" />
                AI Powered
              </Badge>
              <Badge variant="outline" className="hidden sm:flex">
                <Zap className="w-3 h-3 mr-1" />
                Real-time
              </Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container-wide py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Welcome Section */}
          <div className="text-center space-y-4">
            <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Transform Ideas into Mathematical Formulas
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Describe what you want to calculate in plain English, or enter mathematical expressions directly. 
              Our AI will help you generate, solve, and visualize complex mathematical problems.
            </p>
          </div>

          {/* Formula Input Component */}
          <FormulaInput 
            onFormulaChange={(formula) => {
              console.log('Formula changed:', formula)
            }}
            onNLPResult={(result) => {
              console.log('NLP result:', result)
            }}
            onEvaluationResult={(result) => {
              console.log('Evaluation result:', result)
            }}
          />

          {/* Feature Cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12">
            <Card className="group hover:shadow-lg transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mb-3">
                  <Brain className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <CardTitle className="text-lg">Natural Language Processing</CardTitle>
                <CardDescription>
                  Convert plain English descriptions into precise mathematical formulas using advanced AI.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="group hover:shadow-lg transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mb-3">
                  <Calculator className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <CardTitle className="text-lg">Step-by-Step Solutions</CardTitle>
                <CardDescription>
                  Get detailed explanations for equation solving, differentiation, integration, and more.
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="group hover:shadow-lg transition-all duration-300">
              <CardHeader>
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mb-3">
                  <BarChart3 className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <CardTitle className="text-lg">Interactive Visualization</CardTitle>
                <CardDescription>
                  Visualize functions, equations, and mathematical concepts with interactive graphs and plots.
                </CardDescription>
              </CardHeader>
            </Card>
          </div>

          {/* Quick Examples */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5 text-yellow-500" />
                Try These Examples
              </CardTitle>
              <CardDescription>
                Click on any example to get started quickly
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                  <h4 className="font-medium mb-2">Quadratic Formula</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    "Solve ax² + bx + c = 0"
                  </p>
                  <code className="text-xs bg-muted px-2 py-1 rounded">
                    (-b ± √(b²-4ac)) / 2a
                  </code>
                </div>

                <div className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                  <h4 className="font-medium mb-2">Derivative</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    "Find the derivative of x³ + 2x² - 5x + 1"
                  </p>
                  <code className="text-xs bg-muted px-2 py-1 rounded">
                    3x² + 4x - 5
                  </code>
                </div>

                <div className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                  <h4 className="font-medium mb-2">Area of Circle</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    "What's the area of a circle with radius 5?"
                  </p>
                  <code className="text-xs bg-muted px-2 py-1 rounded">
                    π × r² = 78.54
                  </code>
                </div>

                <div className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                  <h4 className="font-medium mb-2">Distance Formula</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    "Distance between points (1,2) and (4,6)"
                  </p>
                  <code className="text-xs bg-muted px-2 py-1 rounded">
                    √((4-1)² + (6-2)²) = 5
                  </code>
                </div>

                <div className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                  <h4 className="font-medium mb-2">Compound Interest</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    "Calculate compound interest for $1000 at 5% for 3 years"
                  </p>
                  <code className="text-xs bg-muted px-2 py-1 rounded">
                    A = P(1 + r)ᵗ = $1157.63
                  </code>
                </div>

                <div className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors">
                  <h4 className="font-medium mb-2">Matrix Multiplication</h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    "Multiply two 2x2 matrices"
                  </p>
                  <code className="text-xs bg-muted px-2 py-1 rounded">
                    [[a,b],[c,d]] × [[e,f],[g,h]]
                  </code>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/30 mt-16">
        <div className="container-wide py-8">
          <div className="text-center text-sm text-muted-foreground">
            <p>© 2024 FormulaForge. Built with Next.js, React, and advanced mathematical engines.</p>
            <p className="mt-2">
              Powered by AI • Real-time Processing • Step-by-Step Solutions
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
