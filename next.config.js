/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['mathjs', 'ml-matrix'],
  },
  
  // Enable React strict mode for better development experience
  reactStrictMode: true,
  
  // Enable SWC minification for better performance
  swcMinify: true,
  
  // Configure images optimization
  images: {
    domains: ['localhost', 'supabase.co'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // Environment variables that should be available on the client side
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // Headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: process.env.NODE_ENV === 'development' ? '*' : 'https://formulaforge.com',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ];
  },
  
  // Webpack configuration for mathematical libraries
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Handle mathematical computation libraries
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      path: false,
      crypto: false,
    };
    
    // Optimize bundle splitting for mathematical libraries
    config.optimization.splitChunks = {
      ...config.optimization.splitChunks,
      cacheGroups: {
        ...config.optimization.splitChunks.cacheGroups,
        math: {
          test: /[\\/]node_modules[\\/](mathjs|ml-matrix|simple-statistics)[\\/]/,
          name: 'math-libs',
          chunks: 'all',
          priority: 10,
        },
        ai: {
          test: /[\\/]node_modules[\\/](@tensorflow|openai)[\\/]/,
          name: 'ai-libs',
          chunks: 'all',
          priority: 10,
        },
        visualization: {
          test: /[\\/]node_modules[\\/](three|d3|recharts)[\\/]/,
          name: 'viz-libs',
          chunks: 'all',
          priority: 10,
        },
      },
    };
    
    // Add support for WebAssembly (for potential future mathematical optimizations)
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
    };
    
    return config;
  },
  
  // Redirects for SEO and user experience
  async redirects() {
    return [
      {
        source: '/formula',
        destination: '/app/formula',
        permanent: true,
      },
      {
        source: '/calculator',
        destination: '/app/calculator',
        permanent: true,
      },
      {
        source: '/graph',
        destination: '/app/visualization',
        permanent: true,
      },
    ];
  },
  
  // Rewrites for API versioning
  async rewrites() {
    return [
      {
        source: '/api/v1/:path*',
        destination: '/api/:path*',
      },
    ];
  },
  
  // Output configuration for deployment
  output: 'standalone',
  
  // Compression for better performance
  compress: true,
  
  // Power by header removal for security
  poweredByHeader: false,
  
  // Generate ETags for caching
  generateEtags: true,
  
  // Page extensions
  pageExtensions: ['ts', 'tsx', 'js', 'jsx', 'md', 'mdx'],
  
  // Trailing slash configuration
  trailingSlash: false,
};

module.exports = nextConfig;
