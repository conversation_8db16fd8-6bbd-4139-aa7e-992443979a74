# FormulaForge Environment Variables
# Development configuration

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=FormulaForge
NEXT_PUBLIC_APP_VERSION=1.0.0

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Placeholder values - replace with actual Supabase project credentials
NEXT_PUBLIC_SUPABASE_URL=https://placeholder.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=placeholder_anon_key
SUPABASE_SERVICE_ROLE_KEY=placeholder_service_role_key

# =============================================================================
# AI/ML SERVICES
# =============================================================================
# OpenAI API for natural language processing (optional)
OPENAI_API_KEY=placeholder_openai_key

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable features during development
NEXT_PUBLIC_ENABLE_AI_DISCOVERY=true
NEXT_PUBLIC_ENABLE_COLLABORATION=true
NEXT_PUBLIC_ENABLE_3D_VISUALIZATION=true
NEXT_PUBLIC_ENABLE_CODE_EXPORT=true
NEXT_PUBLIC_ENABLE_PREMIUM_FEATURES=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
DEBUG_MODE=false
VERBOSE_LOGGING=false
ENABLE_PERFORMANCE_MONITORING=true
