import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { rateLimit } from '@/lib/rate-limit'

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Rate limiting
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500,
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 20, 'CACHE_TOKEN') // 20 room creations per minute
    } catch {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { name, description, isPublic = false, maxParticipants = 10 } = body

    // Validate input
    if (!name || typeof name !== 'string' || name.length < 3) {
      return NextResponse.json(
        { error: 'Room name must be at least 3 characters long' },
        { status: 400 }
      )
    }

    // Get user from authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authorization' },
        { status: 401 }
      )
    }

    // Generate room ID
    const roomId = `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Create room in database
    const { data: room, error: roomError } = await supabase
      .from('collaboration_rooms')
      .insert({
        id: roomId,
        name,
        description,
        is_public: isPublic,
        max_participants: maxParticipants,
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (roomError) {
      console.error('Room creation error:', roomError)
      return NextResponse.json(
        { error: 'Failed to create room' },
        { status: 500 }
      )
    }

    // Add creator as first participant
    await supabase
      .from('room_participants')
      .insert({
        room_id: roomId,
        user_id: user.id,
        role: 'owner',
        joined_at: new Date().toISOString(),
      })

    return NextResponse.json({
      success: true,
      room: {
        id: roomId,
        name,
        description,
        isPublic,
        maxParticipants,
        createdBy: user.id,
        createdAt: room.created_at,
        participantCount: 1,
      },
    })

  } catch (error) {
    console.error('Room creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 100, 'CACHE_TOKEN') // 100 requests per minute
    } catch {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 50)
    const isPublic = searchParams.get('public') === 'true'
    const search = searchParams.get('search')

    let query = supabase
      .from('collaboration_rooms')
      .select(`
        *,
        room_participants(count),
        profiles:created_by(email, full_name)
      `)

    // Filter by public rooms if specified
    if (isPublic) {
      query = query.eq('is_public', true)
    }

    // Search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }

    // Pagination
    const from = (page - 1) * limit
    const to = from + limit - 1

    const { data: rooms, error, count } = await query
      .range(from, to)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Rooms fetch error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch rooms' },
        { status: 500 }
      )
    }

    // Format response
    const formattedRooms = rooms?.map(room => ({
      id: room.id,
      name: room.name,
      description: room.description,
      isPublic: room.is_public,
      maxParticipants: room.max_participants,
      participantCount: room.room_participants?.[0]?.count || 0,
      createdBy: {
        id: room.created_by,
        email: room.profiles?.email,
        name: room.profiles?.full_name,
      },
      createdAt: room.created_at,
      updatedAt: room.updated_at,
    })) || []

    return NextResponse.json({
      success: true,
      rooms: formattedRooms,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    })

  } catch (error) {
    console.error('Rooms fetch error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 10, 'CACHE_TOKEN') // 10 deletions per minute
    } catch {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const roomId = searchParams.get('roomId')

    if (!roomId) {
      return NextResponse.json(
        { error: 'Room ID is required' },
        { status: 400 }
      )
    }

    // Get user from authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authorization' },
        { status: 401 }
      )
    }

    // Check if user is the room owner
    const { data: room, error: roomError } = await supabase
      .from('collaboration_rooms')
      .select('created_by')
      .eq('id', roomId)
      .single()

    if (roomError || !room) {
      return NextResponse.json(
        { error: 'Room not found' },
        { status: 404 }
      )
    }

    if (room.created_by !== user.id) {
      return NextResponse.json(
        { error: 'Only room owner can delete the room' },
        { status: 403 }
      )
    }

    // Delete room (cascade will handle participants and formulas)
    const { error: deleteError } = await supabase
      .from('collaboration_rooms')
      .delete()
      .eq('id', roomId)

    if (deleteError) {
      console.error('Room deletion error:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete room' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Room deleted successfully',
    })

  } catch (error) {
    console.error('Room deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
