/**
 * Validation utilities for FormulaForge backend
 */

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings?: string[]
}

export class ValidationError extends Error {
  constructor(public errors: string[]) {
    super(`Validation failed: ${errors.join(', ')}`)
    this.name = 'ValidationError'
  }
}

/**
 * Validates mathematical formulas for safety and correctness
 */
export function validateFormula(formula: string): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // Basic checks
  if (!formula || typeof formula !== 'string') {
    errors.push('Formula must be a non-empty string')
    return { isValid: false, errors, warnings }
  }

  if (formula.length > 1000) {
    errors.push('Formula too long (max 1000 characters)')
  }

  // Check for dangerous patterns
  const dangerousPatterns = [
    /import\s+/i,
    /require\s*\(/i,
    /eval\s*\(/i,
    /function\s*\(/i,
    /=>\s*{/i,
    /process\./i,
    /global\./i,
    /window\./i,
    /document\./i,
    /console\./i,
    /setTimeout/i,
    /setInterval/i,
    /fetch\s*\(/i,
    /XMLHttpRequest/i,
    /localStorage/i,
    /sessionStorage/i
  ]

  dangerousPatterns.forEach((pattern, index) => {
    if (pattern.test(formula)) {
      errors.push(`Potentially unsafe pattern detected: ${pattern.source}`)
    }
  })

  // Check for balanced parentheses
  let parenCount = 0
  let bracketCount = 0
  let braceCount = 0

  for (const char of formula) {
    switch (char) {
      case '(':
        parenCount++
        break
      case ')':
        parenCount--
        if (parenCount < 0) {
          errors.push('Unmatched closing parenthesis')
        }
        break
      case '[':
        bracketCount++
        break
      case ']':
        bracketCount--
        if (bracketCount < 0) {
          errors.push('Unmatched closing bracket')
        }
        break
      case '{':
        braceCount++
        break
      case '}':
        braceCount--
        if (braceCount < 0) {
          errors.push('Unmatched closing brace')
        }
        break
    }
  }

  if (parenCount !== 0) {
    errors.push('Unmatched parentheses')
  }
  if (bracketCount !== 0) {
    errors.push('Unmatched brackets')
  }
  if (braceCount !== 0) {
    errors.push('Unmatched braces')
  }

  // Check for valid mathematical operators
  const invalidOperatorPatterns = [
    /\+{2,}/,  // Multiple plus signs
    /\-{3,}/,  // More than 2 minus signs
    /\*{2,}/,  // Multiple multiplication signs
    /\/{2,}/,  // Multiple division signs
    /={2,}/,   // Multiple equals signs
  ]

  invalidOperatorPatterns.forEach(pattern => {
    if (pattern.test(formula)) {
      warnings.push('Potentially invalid operator sequence detected')
    }
  })

  // Check for extremely large numbers
  const numberPattern = /\d{10,}/g
  if (numberPattern.test(formula)) {
    warnings.push('Very large numbers detected - may cause precision issues')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validates user input for collaboration features
 */
export function validateCollaborationInput(input: {
  sessionName?: string
  description?: string
  email?: string
  message?: string
}): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (input.sessionName !== undefined) {
    if (!input.sessionName || input.sessionName.trim().length === 0) {
      errors.push('Session name is required')
    } else if (input.sessionName.length > 100) {
      errors.push('Session name too long (max 100 characters)')
    } else if (input.sessionName.length < 3) {
      warnings.push('Session name is quite short')
    }
  }

  if (input.description !== undefined) {
    if (input.description.length > 500) {
      errors.push('Description too long (max 500 characters)')
    }
  }

  if (input.email !== undefined) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailPattern.test(input.email)) {
      errors.push('Invalid email format')
    }
  }

  if (input.message !== undefined) {
    if (input.message.length > 1000) {
      errors.push('Message too long (max 1000 characters)')
    }
    
    // Check for spam-like patterns
    const spamPatterns = [
      /(.)\1{10,}/i,  // Repeated characters
      /https?:\/\/[^\s]+/gi,  // URLs
      /\b(buy|sell|click|free|money|prize|winner)\b/gi  // Spam keywords
    ]
    
    spamPatterns.forEach(pattern => {
      if (pattern.test(input.message)) {
        warnings.push('Message may contain spam-like content')
      }
    })
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Validates API request parameters
 */
export function validateApiParams(params: Record<string, any>, schema: Record<string, {
  required?: boolean
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  min?: number
  max?: number
  pattern?: RegExp
}>): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  for (const [key, rules] of Object.entries(schema)) {
    const value = params[key]

    // Check required fields
    if (rules.required && (value === undefined || value === null)) {
      errors.push(`${key} is required`)
      continue
    }

    // Skip validation if field is not provided and not required
    if (value === undefined || value === null) {
      continue
    }

    // Type validation
    switch (rules.type) {
      case 'string':
        if (typeof value !== 'string') {
          errors.push(`${key} must be a string`)
        } else {
          if (rules.min && value.length < rules.min) {
            errors.push(`${key} must be at least ${rules.min} characters`)
          }
          if (rules.max && value.length > rules.max) {
            errors.push(`${key} must be at most ${rules.max} characters`)
          }
          if (rules.pattern && !rules.pattern.test(value)) {
            errors.push(`${key} format is invalid`)
          }
        }
        break

      case 'number':
        if (typeof value !== 'number' || !isFinite(value)) {
          errors.push(`${key} must be a valid number`)
        } else {
          if (rules.min !== undefined && value < rules.min) {
            errors.push(`${key} must be at least ${rules.min}`)
          }
          if (rules.max !== undefined && value > rules.max) {
            errors.push(`${key} must be at most ${rules.max}`)
          }
        }
        break

      case 'boolean':
        if (typeof value !== 'boolean') {
          errors.push(`${key} must be a boolean`)
        }
        break

      case 'array':
        if (!Array.isArray(value)) {
          errors.push(`${key} must be an array`)
        } else {
          if (rules.min && value.length < rules.min) {
            errors.push(`${key} must have at least ${rules.min} items`)
          }
          if (rules.max && value.length > rules.max) {
            errors.push(`${key} must have at most ${rules.max} items`)
          }
        }
        break

      case 'object':
        if (typeof value !== 'object' || Array.isArray(value)) {
          errors.push(`${key} must be an object`)
        }
        break
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Sanitizes user input to prevent XSS and other attacks
 */
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') {
    return ''
  }

  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
}

/**
 * Validates file uploads
 */
export function validateFileUpload(file: {
  name: string
  size: number
  type: string
}): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // File size limit (5MB)
  const maxSize = 5 * 1024 * 1024
  if (file.size > maxSize) {
    errors.push('File size too large (max 5MB)')
  }

  // Allowed file types
  const allowedTypes = [
    'text/plain',
    'application/json',
    'text/csv',
    'application/pdf',
    'image/png',
    'image/jpeg',
    'image/svg+xml'
  ]

  if (!allowedTypes.includes(file.type)) {
    errors.push('File type not allowed')
  }

  // File name validation
  if (file.name.length > 255) {
    errors.push('File name too long')
  }

  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com']
  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
  if (dangerousExtensions.includes(extension)) {
    errors.push('Dangerous file extension detected')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}
