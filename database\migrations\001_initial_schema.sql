-- FormulaForge Database Schema
-- Initial migration for core tables

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types
CREATE TYPE user_role AS ENUM ('user', 'premium', 'admin');
CREATE TYPE room_role AS ENUM ('owner', 'moderator', 'member');
CREATE TYPE formula_category AS ENUM (
  'algebra', 'calculus', 'geometry', 'trigonometry', 
  'statistics', 'physics', 'chemistry', 'engineering',
  'finance', 'custom'
);

-- Profiles table (extends Supabase auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  role user_role DEFAULT 'user',
  subscription_tier TEXT DEFAULT 'free',
  subscription_expires_at TIMESTAMPTZ,
  preferences JSONB DEFAULT '{}',
  usage_stats JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Formula library table
CREATE TABLE formula_library (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  formula TEXT NOT NULL,
  category formula_category DEFAULT 'custom',
  tags TEXT[] DEFAULT '{}',
  variables JSONB DEFAULT '[]',
  examples JSONB DEFAULT '[]',
  is_public BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  usage_count INTEGER DEFAULT 0,
  rating DECIMAL(3,2) DEFAULT 0,
  rating_count INTEGER DEFAULT 0
);

-- Collaboration rooms table
CREATE TABLE collaboration_rooms (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  max_participants INTEGER DEFAULT 10,
  settings JSONB DEFAULT '{}',
  created_by UUID REFERENCES profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  last_activity_at TIMESTAMPTZ DEFAULT NOW()
);

-- Room participants table
CREATE TABLE room_participants (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  room_id TEXT REFERENCES collaboration_rooms(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  role room_role DEFAULT 'member',
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  last_seen_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(room_id, user_id)
);

-- Shared formulas in rooms
CREATE TABLE room_formulas (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  room_id TEXT REFERENCES collaboration_rooms(id) ON DELETE CASCADE,
  formula_id TEXT,
  formula_data JSONB NOT NULL,
  shared_by UUID REFERENCES profiles(id) ON DELETE CASCADE,
  shared_at TIMESTAMPTZ DEFAULT NOW(),
  version INTEGER DEFAULT 1,
  is_active BOOLEAN DEFAULT TRUE
);

-- Formula ratings table
CREATE TABLE formula_ratings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  formula_id TEXT REFERENCES formula_library(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  review TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(formula_id, user_id)
);

-- User sessions and activity tracking
CREATE TABLE user_sessions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  session_data JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  last_activity_at TIMESTAMPTZ DEFAULT NOW(),
  ended_at TIMESTAMPTZ
);

-- API usage tracking
CREATE TABLE api_usage (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  endpoint TEXT NOT NULL,
  method TEXT NOT NULL,
  status_code INTEGER,
  response_time_ms INTEGER,
  request_size_bytes INTEGER,
  response_size_bytes INTEGER,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Formula computation history
CREATE TABLE computation_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  input_text TEXT,
  formula TEXT,
  operation_type TEXT,
  result JSONB,
  confidence_score DECIMAL(3,2),
  processing_time_ms INTEGER,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Saved workspaces/projects
CREATE TABLE workspaces (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  data JSONB DEFAULT '{}',
  is_public BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Workspace collaborators
CREATE TABLE workspace_collaborators (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  role TEXT DEFAULT 'viewer',
  invited_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
  invited_at TIMESTAMPTZ DEFAULT NOW(),
  accepted_at TIMESTAMPTZ,
  UNIQUE(workspace_id, user_id)
);

-- Create indexes for performance
CREATE INDEX idx_formula_library_created_by ON formula_library(created_by);
CREATE INDEX idx_formula_library_category ON formula_library(category);
CREATE INDEX idx_formula_library_is_public ON formula_library(is_public);
CREATE INDEX idx_formula_library_tags ON formula_library USING GIN(tags);
CREATE INDEX idx_formula_library_usage_count ON formula_library(usage_count DESC);
CREATE INDEX idx_formula_library_rating ON formula_library(rating DESC);
CREATE INDEX idx_formula_library_created_at ON formula_library(created_at DESC);

CREATE INDEX idx_collaboration_rooms_created_by ON collaboration_rooms(created_by);
CREATE INDEX idx_collaboration_rooms_is_public ON collaboration_rooms(is_public);
CREATE INDEX idx_collaboration_rooms_last_activity ON collaboration_rooms(last_activity_at DESC);

CREATE INDEX idx_room_participants_room_id ON room_participants(room_id);
CREATE INDEX idx_room_participants_user_id ON room_participants(user_id);

CREATE INDEX idx_room_formulas_room_id ON room_formulas(room_id);
CREATE INDEX idx_room_formulas_shared_at ON room_formulas(shared_at DESC);

CREATE INDEX idx_api_usage_user_id ON api_usage(user_id);
CREATE INDEX idx_api_usage_endpoint ON api_usage(endpoint);
CREATE INDEX idx_api_usage_created_at ON api_usage(created_at DESC);

CREATE INDEX idx_computation_history_user_id ON computation_history(user_id);
CREATE INDEX idx_computation_history_created_at ON computation_history(created_at DESC);

CREATE INDEX idx_workspaces_created_by ON workspaces(created_by);
CREATE INDEX idx_workspaces_is_public ON workspaces(is_public);

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_profiles_updated_at 
  BEFORE UPDATE ON profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_formula_library_updated_at 
  BEFORE UPDATE ON formula_library 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_collaboration_rooms_updated_at 
  BEFORE UPDATE ON collaboration_rooms 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workspaces_updated_at 
  BEFORE UPDATE ON workspaces 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to update formula ratings
CREATE OR REPLACE FUNCTION update_formula_rating()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE formula_library 
  SET 
    rating = (
      SELECT AVG(rating)::DECIMAL(3,2) 
      FROM formula_ratings 
      WHERE formula_id = NEW.formula_id
    ),
    rating_count = (
      SELECT COUNT(*) 
      FROM formula_ratings 
      WHERE formula_id = NEW.formula_id
    )
  WHERE id = NEW.formula_id;
  
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for formula rating updates
CREATE TRIGGER update_formula_rating_trigger
  AFTER INSERT OR UPDATE OR DELETE ON formula_ratings
  FOR EACH ROW EXECUTE FUNCTION update_formula_rating();

-- Create function to update room activity
CREATE OR REPLACE FUNCTION update_room_activity()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE collaboration_rooms 
  SET last_activity_at = NOW()
  WHERE id = NEW.room_id;
  
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for room activity updates
CREATE TRIGGER update_room_activity_trigger
  AFTER INSERT ON room_formulas
  FOR EACH ROW EXECUTE FUNCTION update_room_activity();

-- Row Level Security (RLS) policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE formula_library ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaboration_rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_formulas ENABLE ROW LEVEL SECURITY;
ALTER TABLE formula_ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_collaborators ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view their own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Formula library policies
CREATE POLICY "Users can view public formulas" ON formula_library
  FOR SELECT USING (is_public = true);

CREATE POLICY "Users can view their own formulas" ON formula_library
  FOR SELECT USING (auth.uid() = created_by);

CREATE POLICY "Users can create formulas" ON formula_library
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own formulas" ON formula_library
  FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own formulas" ON formula_library
  FOR DELETE USING (auth.uid() = created_by);

-- Collaboration rooms policies
CREATE POLICY "Users can view public rooms" ON collaboration_rooms
  FOR SELECT USING (is_public = true);

CREATE POLICY "Users can view rooms they participate in" ON collaboration_rooms
  FOR SELECT USING (
    id IN (
      SELECT room_id FROM room_participants 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create rooms" ON collaboration_rooms
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Room owners can update their rooms" ON collaboration_rooms
  FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Room owners can delete their rooms" ON collaboration_rooms
  FOR DELETE USING (auth.uid() = created_by);

-- Room participants policies
CREATE POLICY "Users can view participants in their rooms" ON room_participants
  FOR SELECT USING (
    room_id IN (
      SELECT room_id FROM room_participants 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can join rooms" ON room_participants
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave rooms" ON room_participants
  FOR DELETE USING (auth.uid() = user_id);

-- Insert default data
INSERT INTO profiles (id, email, full_name, role) 
VALUES (
  '00000000-0000-0000-0000-000000000000',
  '<EMAIL>',
  'System',
  'admin'
) ON CONFLICT (id) DO NOTHING;
