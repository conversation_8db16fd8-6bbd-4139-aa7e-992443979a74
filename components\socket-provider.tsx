"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { io, Socket } from 'socket.io-client'

interface SocketContextType {
  socket: Socket | null
  isConnected: boolean
  joinRoom: (roomId: string) => void
  leaveRoom: (roomId: string) => void
  sendMessage: (event: string, data: any) => void
}

const SocketContext = createContext<SocketContextType | undefined>(undefined)

export function SocketProvider({ children }: { children: React.ReactNode }) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)

  useEffect(() => {
    // Initialize socket connection
    const socketInstance = io(process.env.NEXT_PUBLIC_SOCKET_URL || '', {
      path: '/api/socket',
      addTrailingSlash: false,
    })

    // Connection event handlers
    socketInstance.on('connect', () => {
      console.log('Socket connected:', socketInstance.id)
      setIsConnected(true)
    })

    socketInstance.on('disconnect', () => {
      console.log('Socket disconnected')
      setIsConnected(false)
    })

    socketInstance.on('connect_error', (error) => {
      console.error('Socket connection error:', error)
      setIsConnected(false)
    })

    // Mathematical collaboration events
    socketInstance.on('formula_updated', (data) => {
      console.log('Formula updated:', data)
      // Handle real-time formula updates
    })

    socketInstance.on('user_joined', (data) => {
      console.log('User joined:', data)
      // Handle user joining collaboration session
    })

    socketInstance.on('user_left', (data) => {
      console.log('User left:', data)
      // Handle user leaving collaboration session
    })

    socketInstance.on('cursor_moved', (data) => {
      console.log('Cursor moved:', data)
      // Handle real-time cursor movements
    })

    setSocket(socketInstance)

    // Cleanup on unmount
    return () => {
      socketInstance.close()
    }
  }, [])

  const joinRoom = (roomId: string) => {
    if (socket) {
      socket.emit('join_room', { roomId })
      console.log(`Joined room: ${roomId}`)
    }
  }

  const leaveRoom = (roomId: string) => {
    if (socket) {
      socket.emit('leave_room', { roomId })
      console.log(`Left room: ${roomId}`)
    }
  }

  const sendMessage = (event: string, data: any) => {
    if (socket && isConnected) {
      socket.emit(event, data)
    } else {
      console.warn('Socket not connected, message not sent:', event, data)
    }
  }

  const value = {
    socket,
    isConnected,
    joinRoom,
    leaveRoom,
    sendMessage,
  }

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  )
}

export function useSocket() {
  const context = useContext(SocketContext)
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider')
  }
  return context
}

// Custom hooks for specific socket functionality
export function useCollaboration(roomId?: string) {
  const { socket, isConnected, joinRoom, leaveRoom, sendMessage } = useSocket()
  const [collaborators, setCollaborators] = useState<any[]>([])
  const [isInRoom, setIsInRoom] = useState(false)

  useEffect(() => {
    if (!socket || !roomId) return

    // Join room when component mounts
    joinRoom(roomId)
    setIsInRoom(true)

    // Listen for collaborator updates
    socket.on('collaborators_updated', (data) => {
      setCollaborators(data.collaborators)
    })

    // Cleanup when component unmounts or roomId changes
    return () => {
      if (isInRoom) {
        leaveRoom(roomId)
        setIsInRoom(false)
      }
      socket.off('collaborators_updated')
    }
  }, [socket, roomId, joinRoom, leaveRoom, isInRoom])

  const shareFormula = (formula: string, metadata?: any) => {
    sendMessage('share_formula', {
      roomId,
      formula,
      metadata,
      timestamp: Date.now(),
    })
  }

  const updateCursor = (position: { x: number; y: number }) => {
    sendMessage('cursor_update', {
      roomId,
      position,
      timestamp: Date.now(),
    })
  }

  const sendChat = (message: string) => {
    sendMessage('chat_message', {
      roomId,
      message,
      timestamp: Date.now(),
    })
  }

  return {
    collaborators,
    isInRoom,
    shareFormula,
    updateCursor,
    sendChat,
    isConnected,
  }
}

export function useRealtimeFormula() {
  const { socket } = useSocket()
  const [sharedFormulas, setSharedFormulas] = useState<any[]>([])

  useEffect(() => {
    if (!socket) return

    socket.on('formula_shared', (data) => {
      setSharedFormulas(prev => [...prev, data])
    })

    socket.on('formula_updated', (data) => {
      setSharedFormulas(prev => 
        prev.map(formula => 
          formula.id === data.id ? { ...formula, ...data } : formula
        )
      )
    })

    return () => {
      socket.off('formula_shared')
      socket.off('formula_updated')
    }
  }, [socket])

  return {
    sharedFormulas,
    setSharedFormulas,
  }
}
