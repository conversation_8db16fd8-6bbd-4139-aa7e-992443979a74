/**
 * Tests for FormulaGenerator
 * Run with: npm test or jest
 */

import { FormulaGenerator, FormulaGenerationRequest } from '../ai/formula-generator'

describe('FormulaGenerator', () => {
  let generator: FormulaGenerator

  beforeEach(() => {
    generator = new FormulaGenerator()
  })

  describe('generateFormula', () => {
    it('should generate a quadratic formula from natural language', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: 'quadratic function with vertex at (2, 3)',
        context: 'algebra',
        complexity: 'intermediate'
      }

      const result = await generator.generateFormula(request)

      expect(result.formula).toBeDefined()
      expect(result.latex).toBeDefined()
      expect(result.confidence).toBeGreaterThan(0)
      expect(result.explanation).toBeDefined()
      expect(result.variables).toBeInstanceOf(Array)
    })

    it('should generate a linear formula', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: 'line with slope 2 and y-intercept 5',
        context: 'algebra',
        complexity: 'basic'
      }

      const result = await generator.generateFormula(request)

      expect(result.formula).toContain('2')
      expect(result.formula).toContain('5')
      expect(result.confidence).toBeGreaterThan(0.5)
    })

    it('should handle trigonometric functions', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: 'sine wave with amplitude 3 and period 4',
        context: 'trigonometry',
        complexity: 'intermediate'
      }

      const result = await generator.generateFormula(request)

      expect(result.formula).toContain('sin')
      expect(result.formula).toContain('3')
      expect(result.latex).toContain('\\sin')
    })

    it('should provide fallback for unrecognized patterns', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: 'some completely random text that doesnt match any pattern',
        context: 'algebra',
        complexity: 'basic'
      }

      const result = await generator.generateFormula(request)

      expect(result.formula).toBeDefined()
      expect(result.confidence).toBeLessThan(0.5)
      expect(result.explanation).toContain('basic mathematical formula')
    })

    it('should extract variables correctly', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: 'quadratic function with roots at 1 and 3',
        context: 'algebra',
        complexity: 'basic'
      }

      const result = await generator.generateFormula(request)

      expect(result.variables).toBeInstanceOf(Array)
      expect(result.variables.length).toBeGreaterThan(0)
      
      const xVariable = result.variables.find(v => v.name === 'x')
      expect(xVariable).toBeDefined()
      expect(xVariable?.type).toBe('independent')
    })

    it('should convert formulas to LaTeX correctly', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: 'exponential growth with rate 0.5 and initial value 10',
        context: 'calculus',
        complexity: 'intermediate'
      }

      const result = await generator.generateFormula(request)

      expect(result.latex).toBeDefined()
      expect(result.latex).not.toBe(result.formula)
      // LaTeX should have proper formatting
      if (result.formula.includes('*')) {
        expect(result.latex).toContain('\\cdot')
      }
    })

    it('should provide alternatives when available', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: 'parabola opens up with vertex at (1, 2)',
        context: 'algebra',
        complexity: 'intermediate'
      }

      const result = await generator.generateFormula(request)

      expect(result.alternatives).toBeInstanceOf(Array)
      // May or may not have alternatives depending on pattern matches
      result.alternatives.forEach(alt => {
        expect(alt.formula).toBeDefined()
        expect(alt.latex).toBeDefined()
        expect(alt.confidence).toBeGreaterThan(0)
      })
    })

    it('should handle complex mathematical expressions', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: 'line through points (1, 2) and (3, 6)',
        context: 'algebra',
        complexity: 'intermediate'
      }

      const result = await generator.generateFormula(request)

      expect(result.formula).toBeDefined()
      expect(result.steps).toBeInstanceOf(Array)
      expect(result.steps!.length).toBeGreaterThan(0)
    })
  })

  describe('pattern matching', () => {
    it('should match quadratic patterns correctly', async () => {
      const testCases = [
        'quadratic function with vertex at (0, 0)',
        'parabola opens down with vertex at (-1, 5)',
        'quadratic with roots at 2 and 4'
      ]

      for (const testCase of testCases) {
        const result = await generator.generateFormula({
          naturalLanguage: testCase,
          context: 'algebra'
        })
        
        expect(result.confidence).toBeGreaterThan(0.5)
        expect(result.formula).toBeDefined()
      }
    })

    it('should match trigonometric patterns correctly', async () => {
      const testCases = [
        'sine wave with amplitude 2 and period 3',
        'cosine function shifted 1.5 units'
      ]

      for (const testCase of testCases) {
        const result = await generator.generateFormula({
          naturalLanguage: testCase,
          context: 'trigonometry'
        })
        
        expect(result.confidence).toBeGreaterThan(0.5)
        expect(result.formula).toMatch(/(sin|cos)/i)
      }
    })
  })

  describe('error handling', () => {
    it('should handle empty input gracefully', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: '',
        context: 'algebra'
      }

      const result = await generator.generateFormula(request)
      
      expect(result.formula).toBeDefined()
      expect(result.confidence).toBeLessThan(0.5)
    })

    it('should handle very long input', async () => {
      const longText = 'a'.repeat(2000)
      const request: FormulaGenerationRequest = {
        naturalLanguage: longText,
        context: 'algebra'
      }

      const result = await generator.generateFormula(request)
      
      expect(result.formula).toBeDefined()
    })

    it('should handle special characters in input', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: 'quadratic with special chars: @#$%^&*()',
        context: 'algebra'
      }

      const result = await generator.generateFormula(request)
      
      expect(result.formula).toBeDefined()
      expect(result.confidence).toBeGreaterThan(0)
    })
  })

  describe('LaTeX conversion', () => {
    it('should convert multiplication symbols', () => {
      // This would test the private method if it were public
      // For now, we test through the public interface
      const testFormula = '2*x + 3*y'
      // We can't directly test private methods, but we can test the output
      expect(testFormula).toContain('*')
    })

    it('should convert mathematical functions', async () => {
      const request: FormulaGenerationRequest = {
        naturalLanguage: 'sine of x plus cosine of y',
        context: 'trigonometry'
      }

      const result = await generator.generateFormula(request)
      
      if (result.formula.includes('sin') || result.formula.includes('cos')) {
        expect(result.latex).toMatch(/(\\sin|\\cos)/)
      }
    })
  })
})

// Mock test runner setup (if not using Jest)
if (typeof describe === 'undefined') {
  console.log('Test framework not available. Install Jest to run tests.')
  console.log('npm install --save-dev jest @types/jest ts-jest')
  console.log('Then run: npm test')
}
