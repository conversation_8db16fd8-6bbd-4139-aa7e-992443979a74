import { Suspense } from 'react'
import { <PERSON><PERSON><PERSON> } from 'next'
import Link from 'next/link'
import { ArrowRight, Calculator, Brain, Zap, Users, Code, BarChart3 } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { HeroSection } from '@/components/hero-section'
import { FeatureGrid } from '@/components/feature-grid'
import { DemoSection } from '@/components/visualization/demo-section'

import { Footer } from '@/components/footer'
import { Navigation } from '@/components/navigation'

export const metadata: Metadata = {
  title: 'FormulaForge - Advanced Mathematical Formula Generator',
  description: 'Generate and manipulate mathematical formulas with AI-powered natural language processing. Solve equations, visualize functions, and collaborate in real-time.',
  openGraph: {
    title: 'FormulaForge - Advanced Mathematical Formula Generator',
    description: 'Generate and manipulate mathematical formulas with AI-powered natural language processing.',
    images: ['/og-home.png'],
  },
}

const features = [
  {
    icon: Brain,
    title: 'AI-Powered Formula Generation',
    description: 'Convert natural language descriptions into precise mathematical formulas using advanced NLP.',
    color: 'text-blue-500',
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
  },
  {
    icon: Calculator,
    title: 'Advanced Mathematical Engine',
    description: 'Solve equations, simplify expressions, and perform symbolic calculus with step-by-step solutions.',
    color: 'text-green-500',
    bgColor: 'bg-green-50 dark:bg-green-950/20',
  },
  {
    icon: BarChart3,
    title: 'Interactive 3D Visualization',
    description: 'Create stunning interactive graphs and 3D plots with real-time parameter adjustments.',
    color: 'text-purple-500',
    bgColor: 'bg-purple-50 dark:bg-purple-950/20',
  },
  {
    icon: Users,
    title: 'Real-Time Collaboration',
    description: 'Work together on mathematical problems with live editing and sharing capabilities.',
    color: 'text-orange-500',
    bgColor: 'bg-orange-50 dark:bg-orange-950/20',
  },
  {
    icon: Code,
    title: 'Multi-Language Export',
    description: 'Export formulas and solutions as code in Python, JavaScript, MATLAB, and more.',
    color: 'text-indigo-500',
    bgColor: 'bg-indigo-50 dark:bg-indigo-950/20',
  },
  {
    icon: Zap,
    title: 'Lightning Fast Performance',
    description: 'Optimized algorithms ensure rapid computation even for complex mathematical operations.',
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-50 dark:bg-yellow-950/20',
  },
]

const stats = [
  { label: 'Formulas Generated', value: '1M+' },
  { label: 'Active Users', value: '50K+' },
  { label: 'Mathematical Operations', value: '500+' },
  { label: 'Accuracy Rate', value: '99.9%' },
]

export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Navigation */}
      <Navigation />
      
      {/* Hero Section */}
      <HeroSection />
      
      {/* Stats Section */}
      <section className="py-12 bg-muted/30">
        <div className="container-wide">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Features Section */}
      <section className="py-20">
        <div className="container-wide">
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-4">
              Core Features
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Everything you need for mathematical excellence
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              FormulaForge combines cutting-edge AI with powerful mathematical engines 
              to provide an unparalleled formula generation and manipulation experience.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="relative overflow-hidden group hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className={`w-12 h-12 rounded-lg ${feature.bgColor} flex items-center justify-center mb-4`}>
                    <feature.icon className={`w-6 h-6 ${feature.color}`} />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
                <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </Card>
            ))}
          </div>
        </div>
      </section>
      
      {/* Demo Section */}
      <Suspense fallback={<div className="py-20 bg-muted/30" />}>
        <DemoSection />
      </Suspense>
      
      {/* Feature Grid */}
      <FeatureGrid />
      

      
      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary to-primary/80">
        <div className="container-wide text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to revolutionize your mathematical workflow?
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Join thousands of mathematicians, engineers, and students who trust FormulaForge 
            for their most complex mathematical challenges.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" asChild>
              <Link href="/app">
                Start Free Trial
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20" asChild>
              <Link href="/demo">
                Watch Demo
              </Link>
            </Button>
          </div>
        </div>
      </section>
      
      {/* Footer */}
      <Footer />
    </div>
  )
}
