# FormulaForge Environment Variables
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=FormulaForge
NEXT_PUBLIC_APP_VERSION=1.0.0

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Get these from your Supabase project settings
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Database URL for direct connections (if needed)
DATABASE_URL=postgresql://username:password@host:port/database

# =============================================================================
# AUTHENTICATION
# =============================================================================
# JWT secret for session management
NEXTAUTH_SECRET=your_nextauth_secret_key_here
NEXTAUTH_URL=http://localhost:3000

# OAuth providers (optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# =============================================================================
# AI/ML SERVICES
# =============================================================================
# OpenAI API for natural language processing
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORGANIZATION=your_openai_organization_id

# Alternative AI providers (optional)
ANTHROPIC_API_KEY=your_anthropic_api_key
HUGGING_FACE_API_KEY=your_hugging_face_api_key

# =============================================================================
# MATHEMATICAL SERVICES
# =============================================================================
# Wolfram Alpha API for advanced mathematical computations
WOLFRAM_ALPHA_APP_ID=your_wolfram_alpha_app_id

# Symbolab API (if available)
SYMBOLAB_API_KEY=your_symbolab_api_key

# Mathpix API for OCR mathematical expressions
MATHPIX_APP_ID=your_mathpix_app_id
MATHPIX_APP_KEY=your_mathpix_app_key

# =============================================================================
# CACHING AND PERFORMANCE
# =============================================================================
# Redis for caching (optional, can use memory cache for development)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# CDN configuration (optional)
NEXT_PUBLIC_CDN_URL=https://your-cdn-domain.com

# =============================================================================
# MONITORING AND ANALYTICS
# =============================================================================
# Error tracking
SENTRY_DSN=your_sentry_dsn
SENTRY_AUTH_TOKEN=your_sentry_auth_token

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga_tracking_id
NEXT_PUBLIC_MIXPANEL_TOKEN=your_mixpanel_token

# Performance monitoring
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_vercel_analytics_id

# =============================================================================
# EMAIL SERVICES
# =============================================================================
# For user notifications and collaboration features
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password

# SendGrid (alternative)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# =============================================================================
# FILE STORAGE
# =============================================================================
# AWS S3 for file uploads (optional, Supabase Storage is primary)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=formulaforge-uploads

# =============================================================================
# REAL-TIME FEATURES
# =============================================================================
# Socket.IO configuration
SOCKET_IO_SECRET=your_socket_io_secret
NEXT_PUBLIC_SOCKET_URL=http://localhost:3000

# =============================================================================
# RATE LIMITING
# =============================================================================
# API rate limiting configuration
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# Premium user limits
PREMIUM_RATE_LIMIT_MAX_REQUESTS=1000
PREMIUM_RATE_LIMIT_WINDOW_MS=900000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable features during development
NEXT_PUBLIC_ENABLE_AI_DISCOVERY=true
NEXT_PUBLIC_ENABLE_COLLABORATION=true
NEXT_PUBLIC_ENABLE_3D_VISUALIZATION=true
NEXT_PUBLIC_ENABLE_CODE_EXPORT=true
NEXT_PUBLIC_ENABLE_PREMIUM_FEATURES=false

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Debug modes
DEBUG_MODE=false
VERBOSE_LOGGING=false
ENABLE_PERFORMANCE_MONITORING=true

# Testing
TEST_DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/formulaforge_test

# =============================================================================
# SECURITY
# =============================================================================
# CORS origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,https://formulaforge.com

# Content Security Policy
CSP_REPORT_URI=https://your-csp-report-endpoint.com

# =============================================================================
# DEPLOYMENT
# =============================================================================
# Build configuration
BUILD_ANALYZE=false
BUILD_STANDALONE=true

# Health check endpoint secret
HEALTH_CHECK_SECRET=your_health_check_secret
