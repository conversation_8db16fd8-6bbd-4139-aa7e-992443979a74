'use client'

import React from 'react'
import { 
  Brain, 
  Calculator, 
  BarChart3, 
  Users, 
  Code, 
  Zap, 
  Target, 
  Layers,
  Sparkles,
  Globe,
  Shield,
  Rocket
} from 'lucide-react'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

const features = [
  {
    icon: Brain,
    title: 'Advanced Natural Language Processing',
    description: 'Convert complex mathematical descriptions into precise formulas using state-of-the-art AI models.',
    category: 'AI/ML',
    color: 'text-blue-500',
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    highlights: ['GPT Integration', 'Context Understanding', '95% Accuracy'],
  },
  {
    icon: Calculator,
    title: 'Comprehensive Mathematical Engine',
    description: 'Solve equations, perform calculus operations, and handle complex mathematical computations with step-by-step explanations.',
    category: 'Core',
    color: 'text-green-500',
    bgColor: 'bg-green-50 dark:bg-green-950/20',
    highlights: ['Symbolic Math', 'Step-by-Step', 'Multiple Methods'],
  },
  {
    icon: Bar<PERSON>hart3,
    title: 'Interactive 3D Visualization',
    description: 'Create stunning interactive graphs, 3D plots, and mathematical visualizations with real-time parameter adjustments.',
    category: 'Visualization',
    color: 'text-purple-500',
    bgColor: 'bg-purple-50 dark:bg-purple-950/20',
    highlights: ['3D Graphics', 'Real-time', 'Interactive'],
  },
  {
    icon: Users,
    title: 'Real-Time Collaboration',
    description: 'Work together on mathematical problems with live editing, sharing, and synchronized cursor movements.',
    category: 'Collaboration',
    color: 'text-orange-500',
    bgColor: 'bg-orange-50 dark:bg-orange-950/20',
    highlights: ['Live Editing', 'Multi-user', 'Sync Cursors'],
  },
  {
    icon: Code,
    title: 'Multi-Language Code Export',
    description: 'Export formulas and solutions as executable code in Python, JavaScript, MATLAB, R, and more programming languages.',
    category: 'Export',
    color: 'text-indigo-500',
    bgColor: 'bg-indigo-50 dark:bg-indigo-950/20',
    highlights: ['10+ Languages', 'Executable Code', 'Optimized'],
  },
  {
    icon: Zap,
    title: 'Lightning-Fast Performance',
    description: 'Optimized algorithms ensure rapid computation even for the most complex mathematical operations and large datasets.',
    category: 'Performance',
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-50 dark:bg-yellow-950/20',
    highlights: ['Sub-second', 'Optimized', 'Scalable'],
  },
  {
    icon: Target,
    title: 'AI-Powered Formula Discovery',
    description: 'Input data points and let our AI discover underlying mathematical relationships and generate fitting formulas.',
    category: 'AI/ML',
    color: 'text-red-500',
    bgColor: 'bg-red-50 dark:bg-red-950/20',
    highlights: ['Pattern Recognition', 'Auto-fitting', 'Smart Analysis'],
  },
  {
    icon: Layers,
    title: 'Custom Formula Libraries',
    description: 'Create, organize, and share your own collections of frequently used formulas and mathematical functions.',
    category: 'Organization',
    color: 'text-teal-500',
    bgColor: 'bg-teal-50 dark:bg-teal-950/20',
    highlights: ['Personal Library', 'Categorization', 'Sharing'],
  },
  {
    icon: Sparkles,
    title: 'Advanced Unit Conversion',
    description: 'Convert between thousands of units across physics, chemistry, engineering, and specialized scientific domains.',
    category: 'Utilities',
    color: 'text-pink-500',
    bgColor: 'bg-pink-50 dark:bg-pink-950/20',
    highlights: ['1000+ Units', 'Scientific', 'Precise'],
  },
  {
    icon: Globe,
    title: 'Matrix & Linear Algebra',
    description: 'Comprehensive matrix operations, eigenvalue calculations, linear system solving, and advanced linear algebra tools.',
    category: 'Mathematics',
    color: 'text-cyan-500',
    bgColor: 'bg-cyan-50 dark:bg-cyan-950/20',
    highlights: ['Matrix Ops', 'Eigenvalues', 'Linear Systems'],
  },
  {
    icon: Shield,
    title: 'Error Analysis & Validation',
    description: 'Robust error detection, formula validation, and accuracy verification to ensure reliable mathematical results.',
    category: 'Quality',
    color: 'text-emerald-500',
    bgColor: 'bg-emerald-50 dark:bg-emerald-950/20',
    highlights: ['Error Detection', 'Validation', '99.9% Accuracy'],
  },
  {
    icon: Rocket,
    title: 'Statistical Analysis Suite',
    description: 'Complete statistical toolkit including regression analysis, probability distributions, and hypothesis testing.',
    category: 'Statistics',
    color: 'text-violet-500',
    bgColor: 'bg-violet-50 dark:bg-violet-950/20',
    highlights: ['Regression', 'Distributions', 'Hypothesis Testing'],
  },
]

const categories = [
  { name: 'All', count: features.length },
  { name: 'AI/ML', count: features.filter(f => f.category === 'AI/ML').length },
  { name: 'Core', count: features.filter(f => f.category === 'Core').length },
  { name: 'Visualization', count: features.filter(f => f.category === 'Visualization').length },
  { name: 'Collaboration', count: features.filter(f => f.category === 'Collaboration').length },
]

export function FeatureGrid() {
  const [selectedCategory, setSelectedCategory] = React.useState('All')

  const filteredFeatures = selectedCategory === 'All' 
    ? features 
    : features.filter(feature => feature.category === selectedCategory)

  return (
    <section className="py-20 bg-muted/30">
      <div className="container-wide">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            Comprehensive Features
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Everything you need for mathematical excellence
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            FormulaForge combines cutting-edge AI with powerful mathematical engines 
            to provide an unparalleled formula generation and manipulation experience.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {categories.map((category) => (
            <button
              key={category.name}
              onClick={() => setSelectedCategory(category.name)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                selectedCategory === category.name
                  ? 'bg-primary text-primary-foreground shadow-md'
                  : 'bg-background hover:bg-muted text-muted-foreground hover:text-foreground'
              }`}
            >
              {category.name}
              <span className="ml-1 text-xs opacity-70">({category.count})</span>
            </button>
          ))}
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredFeatures.map((feature, index) => (
            <Card 
              key={index} 
              className="group relative overflow-hidden hover:shadow-xl transition-all duration-300 border-0 bg-background/60 backdrop-blur-sm"
            >
              <CardHeader className="pb-4">
                <div className={`w-14 h-14 rounded-xl ${feature.bgColor} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className={`w-7 h-7 ${feature.color}`} />
                </div>
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="outline" className="text-xs">
                    {feature.category}
                  </Badge>
                </div>
                <CardTitle className="text-xl leading-tight">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <CardDescription className="text-base leading-relaxed">
                  {feature.description}
                </CardDescription>
                
                <div className="flex flex-wrap gap-1">
                  {feature.highlights.map((highlight, idx) => (
                    <Badge 
                      key={idx} 
                      variant="secondary" 
                      className="text-xs px-2 py-1"
                    >
                      {highlight}
                    </Badge>
                  ))}
                </div>
              </CardContent>
              
              {/* Hover Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-muted-foreground mb-6">
            Ready to experience the future of mathematical computation?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium hover:bg-primary/90 transition-colors">
              Start Free Trial
            </button>
            <button className="px-8 py-3 border border-border rounded-lg font-medium hover:bg-muted transition-colors">
              View Documentation
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}
