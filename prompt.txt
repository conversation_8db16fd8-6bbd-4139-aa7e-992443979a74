You are an expert software developer tasked with creating a web application that replicates and surpasses the functionality of formulabot.com. Your goal is to build a superior tool for generating and manipulating formulas, equations, and mathematical expressions.

Core Requirements:

Replicate formulabot.com's core functionality:
Implement all existing formula generation and manipulation features, including but not limited to:
Generating formulas from natural language descriptions.
Solving equations.
Simplifying expressions.
Converting between units.
Graphing functions.
Ensure accurate and efficient formula processing.
Enhance existing features:
Improved Natural Language Processing (NLP): Develop a more sophisticated NLP engine capable of understanding complex and ambiguous mathematical requests, including contextual understanding and the ability to handle a wider range of mathematical notations and terminology.
Advanced Formula Optimization: implement algorithms that not only solve equations, but also optimize them for computational efficiency, minimizing the number of operations or memory usage.
Interactive Visualization: create an interactive graphing tool with 3D plotting capabilities, dynamic parameter adjustments, and the ability to visualize complex mathematical concepts.
Step-by-Step Solutions: Provide detailed, step-by-step solutions for equation solving and simplification, with clear explanations of each step.
Customizable Formula Libraries: Allow users to create and save their own libraries of frequently used formulas and functions.
Unit Conversion Enhancement: significantly expand the unit conversion capabilities, including less common and specialized units.
Error Analysis and Validation: implement robust error detection and validation to ensure the accuracy of generated formulas and solutions.
Introduce novel features to surpass formulabot.com:
Symbolic Differentiation and Integration: Add the ability to perform symbolic differentiation and integration of complex functions.
Linear Algebra and Matrix Operations: Incorporate tools for matrix operations, linear equation solving, eigenvalue/eigenvector calculations, and other linear algebra tasks.
Statistical Analysis: Integrate statistical functions for data analysis, including regression, probability distributions, and hypothesis testing.
Programming Language Integration: Allow users to export generated formulas and solutions as code snippets in multiple programming languages (e.g., Python, JavaScript, MATLAB).
Collaborative Features: Enable users to collaborate on formulas and projects, sharing and editing formulas in real-time.
AI powered formula discovery: Allow the user to input data, and have the AI attempt to generate a formula that fits that data.
User Interface (UI) and User Experience (UX):
Design a clean, intuitive, and user-friendly interface.
Ensure responsiveness across different devices.
Implement robust error handling and informative feedback.
Performance and Scalability:
Optimize the application for speed and efficiency.
Design the architecture to handle a large number of concurrent users.
Deliverables:

A fully functional web application with all specified features.
Comprehensive documentation, including API documentation (if applicable).
Source code with comments.
Success Metrics:

Accuracy and speed of formula generation and manipulation.
User satisfaction and adoption.
Performance benchmarks compared to formulabot.com.
The ability to accurately and efficiently handle complex mathematical problems that formulabot.com cannot.
Please provide a detailed plan, including the architecture, technologies to be used, and a timeline for development.

My github is https://github.com/HectorTa1989. Show me github readme with some good product names that nobody registered website domain with those names before, system architecture in mermaid syntax, workflow in mermaid syntax, Project structure all in github readme. Then code for each file in the project structure in separate artifacts (each file in 1 block) with exact file path, file name. Write commit message for each file after each file, so I can commit to github. Code using our own algorithms and free APIs is better