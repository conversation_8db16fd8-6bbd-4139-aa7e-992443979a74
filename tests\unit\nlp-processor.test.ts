import { NLPProcessor } from '@/backend/nlp/nlp-processor'

describe('NLPProcessor', () => {
  let processor: NLPProcessor

  beforeEach(() => {
    processor = new NLPProcessor()
  })

  describe('processNaturalLanguage', () => {
    it('should process simple mathematical requests', async () => {
      const result = await processor.processNaturalLanguage('solve x plus 5 equals 10')
      
      expect(result.intent).toBe('solve')
      expect(result.formula).toContain('x')
      expect(result.formula).toContain('5')
      expect(result.formula).toContain('10')
      expect(result.confidence).toBeGreaterThan(0.5)
    })

    it('should extract variables from natural language', async () => {
      const result = await processor.processNaturalLanguage('find the derivative of x squared with respect to x')
      
      expect(result.intent).toBe('derivative')
      expect(result.variables).toContain('x')
      expect(result.formula).toContain('x')
      expect(result.confidence).toBeGreaterThan(0.7)
    })

    it('should handle quadratic formula requests', async () => {
      const result = await processor.processNaturalLanguage('use the quadratic formula')
      
      expect(result.intent).toBe('solve')
      expect(result.formula).toContain('sqrt')
      expect(result.formula).toContain('b^2')
      expect(result.variables).toEqual(expect.arrayContaining(['a', 'b', 'c']))
    })

    it('should process distance formula requests', async () => {
      const result = await processor.processNaturalLanguage('calculate distance between two points')
      
      expect(result.intent).toBe('solve')
      expect(result.formula).toContain('sqrt')
      expect(result.variables).toEqual(expect.arrayContaining(['x1', 'y1', 'x2', 'y2']))
    })

    it('should handle area calculations', async () => {
      const result = await processor.processNaturalLanguage('find the area of a circle')
      
      expect(result.intent).toBe('solve')
      expect(result.formula).toContain('pi')
      expect(result.formula).toContain('r')
      expect(result.variables).toContain('r')
    })

    it('should process derivative requests', async () => {
      const result = await processor.processNaturalLanguage('differentiate x cubed plus 2x')
      
      expect(result.intent).toBe('derivative')
      expect(result.formula).toContain('x')
      expect(result.confidence).toBeGreaterThan(0.6)
    })

    it('should handle integral requests', async () => {
      const result = await processor.processNaturalLanguage('integrate x squared')
      
      expect(result.intent).toBe('integral')
      expect(result.formula).toContain('x')
      expect(result.confidence).toBeGreaterThan(0.6)
    })

    it('should process statistical requests', async () => {
      const result = await processor.processNaturalLanguage('calculate the mean of these numbers')
      
      expect(result.intent).toBe('statistics')
      expect(result.confidence).toBeGreaterThan(0.5)
    })

    it('should handle compound interest calculations', async () => {
      const result = await processor.processNaturalLanguage('calculate compound interest')
      
      expect(result.intent).toBe('solve')
      expect(result.formula).toContain('P')
      expect(result.variables).toEqual(expect.arrayContaining(['P', 'r', 'n', 't']))
    })

    it('should process slope calculations', async () => {
      const result = await processor.processNaturalLanguage('find the slope between two points')
      
      expect(result.intent).toBe('solve')
      expect(result.formula).toContain('y2')
      expect(result.formula).toContain('y1')
      expect(result.variables).toEqual(expect.arrayContaining(['x1', 'y1', 'x2', 'y2']))
    })
  })

  describe('intent extraction', () => {
    it('should identify solve intent', async () => {
      const inputs = [
        'solve this equation',
        'find the value of x',
        'calculate the result',
        'determine the answer'
      ]

      for (const input of inputs) {
        const result = await processor.processNaturalLanguage(input)
        expect(result.intent).toBe('solve')
      }
    })

    it('should identify derivative intent', async () => {
      const inputs = [
        'find the derivative',
        'differentiate this function',
        'what is the rate of change',
        'calculate the slope'
      ]

      for (const input of inputs) {
        const result = await processor.processNaturalLanguage(input)
        expect(result.intent).toBe('derivative')
      }
    })

    it('should identify integral intent', async () => {
      const inputs = [
        'integrate this function',
        'find the integral',
        'calculate the area under the curve',
        'find the antiderivative'
      ]

      for (const input of inputs) {
        const result = await processor.processNaturalLanguage(input)
        expect(result.intent).toBe('integral')
      }
    })

    it('should identify simplify intent', async () => {
      const inputs = [
        'simplify this expression',
        'reduce this equation',
        'minimize this formula'
      ]

      for (const input of inputs) {
        const result = await processor.processNaturalLanguage(input)
        expect(result.intent).toBe('simplify')
      }
    })
  })

  describe('entity extraction', () => {
    it('should extract numbers from text', async () => {
      const result = await processor.processNaturalLanguage('solve 2x plus 5 equals 15')
      
      expect(result.entities.numbers).toEqual(expect.arrayContaining([2, 5, 15]))
    })

    it('should extract mathematical functions', async () => {
      const result = await processor.processNaturalLanguage('find the derivative of sin x plus cos x')
      
      expect(result.entities.functions).toEqual(expect.arrayContaining(['sin', 'cos']))
    })

    it('should extract operations', async () => {
      const result = await processor.processNaturalLanguage('add x and y then multiply by z')
      
      expect(result.entities.operations).toEqual(expect.arrayContaining(['+', '*']))
    })

    it('should handle complex expressions with multiple entities', async () => {
      const result = await processor.processNaturalLanguage('solve sin(2x) plus 3 equals 7')
      
      expect(result.entities.numbers).toEqual(expect.arrayContaining([2, 3, 7]))
      expect(result.entities.functions).toContain('sin')
      expect(result.entities.operations).toContain('+')
    })
  })

  describe('variable extraction', () => {
    it('should extract single variables', async () => {
      const result = await processor.processNaturalLanguage('solve for x')
      
      expect(result.variables).toContain('x')
    })

    it('should extract multiple variables', async () => {
      const result = await processor.processNaturalLanguage('find x and y when x plus y equals 10')
      
      expect(result.variables).toEqual(expect.arrayContaining(['x', 'y']))
    })

    it('should handle explicit variable mentions', async () => {
      const result = await processor.processNaturalLanguage('differentiate with respect to variable t')
      
      expect(result.variables).toContain('t')
    })

    it('should filter out common words', async () => {
      const result = await processor.processNaturalLanguage('find a solution to this equation')
      
      expect(result.variables).not.toContain('a')
      expect(result.variables).not.toContain('to')
    })
  })

  describe('confidence scoring', () => {
    it('should give high confidence for clear mathematical requests', async () => {
      const result = await processor.processNaturalLanguage('solve x squared equals 16')
      
      expect(result.confidence).toBeGreaterThan(0.8)
    })

    it('should give lower confidence for ambiguous requests', async () => {
      const result = await processor.processNaturalLanguage('do something with numbers')
      
      expect(result.confidence).toBeLessThan(0.7)
    })

    it('should give medium confidence for partial matches', async () => {
      const result = await processor.processNaturalLanguage('calculate something with x')
      
      expect(result.confidence).toBeGreaterThan(0.4)
      expect(result.confidence).toBeLessThan(0.8)
    })
  })

  describe('formula construction', () => {
    it('should construct formulas from arithmetic descriptions', async () => {
      const result = await processor.processNaturalLanguage('add 5 and 3 then multiply by 2')
      
      expect(result.formula).toMatch(/5.*3.*2/)
      expect(result.confidence).toBeGreaterThan(0.6)
    })

    it('should handle function-based descriptions', async () => {
      const result = await processor.processNaturalLanguage('take the sine of x')
      
      expect(result.formula).toContain('sin')
      expect(result.formula).toContain('x')
    })

    it('should construct complex expressions', async () => {
      const result = await processor.processNaturalLanguage('x squared plus 2x minus 1')
      
      expect(result.formula).toMatch(/x.*2.*x.*1/)
      expect(result.confidence).toBeGreaterThan(0.7)
    })
  })

  describe('error handling', () => {
    it('should handle empty input', async () => {
      const result = await processor.processNaturalLanguage('')
      
      expect(result.confidence).toBeLessThan(0.5)
      expect(result.formula).toBeFalsy()
    })

    it('should handle non-mathematical input', async () => {
      const result = await processor.processNaturalLanguage('what is the weather today')
      
      expect(result.confidence).toBeLessThan(0.3)
      expect(result.intent).toBe('evaluate') // default intent
    })

    it('should handle very long input', async () => {
      const longInput = 'a'.repeat(1000) + ' solve x'
      const result = await processor.processNaturalLanguage(longInput)
      
      expect(result).toBeDefined()
      expect(result.confidence).toBeGreaterThan(0)
    })
  })

  describe('pattern matching', () => {
    it('should match known mathematical patterns', async () => {
      const patterns = [
        'quadratic formula',
        'distance formula',
        'pythagorean theorem',
        'area of circle',
        'compound interest'
      ]

      for (const pattern of patterns) {
        const result = await processor.processNaturalLanguage(pattern)
        expect(result.confidence).toBeGreaterThan(0.8)
        expect(result.formula).toBeDefined()
        expect(result.variables).toBeDefined()
      }
    })

    it('should provide explanations for matched patterns', async () => {
      const result = await processor.processNaturalLanguage('quadratic formula')
      
      expect(result.explanation).toContain('quadratic')
      expect(result.explanation).toBeDefined()
    })
  })
})
