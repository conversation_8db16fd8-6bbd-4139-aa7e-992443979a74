import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const cardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm",
  {
    variants: {
      variant: {
        default: "",
        formula: "formula-container border-formula-200 dark:border-formula-800",
        math: "bg-math-50 dark:bg-math-950/20 border-math-200 dark:border-math-800",
        graph: "graph-container border-graph-200 dark:border-graph-800",
        interactive: "hover:shadow-md transition-all duration-200 cursor-pointer hover:scale-[1.02]",
        elevated: "shadow-lg hover:shadow-xl transition-shadow duration-200",
      },
      padding: {
        none: "",
        sm: "p-3",
        default: "p-6",
        lg: "p-8",
      },
    },
    defaultVariants: {
      variant: "default",
      padding: "default",
    },
  }
)

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, padding, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardVariants({ variant, padding, className }))}
      {...props}
    />
  )
)
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

// Mathematical Card Components
const FormulaCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    title?: string
    formula?: string
    description?: string
    interactive?: boolean
  }
>(({ className, title, formula, description, interactive = false, children, ...props }, ref) => (
  <Card
    ref={ref}
    variant={interactive ? "interactive" : "formula"}
    className={cn("relative overflow-hidden", className)}
    {...props}
  >
    {title && (
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">{title}</CardTitle>
        {description && (
          <CardDescription>{description}</CardDescription>
        )}
      </CardHeader>
    )}
    <CardContent className="space-y-4">
      {formula && (
        <div className="math-expression text-center py-4 px-2 bg-muted/30 rounded-md">
          <div className="katex-display">
            {formula}
          </div>
        </div>
      )}
      {children}
    </CardContent>
  </Card>
))
FormulaCard.displayName = "FormulaCard"

const GraphCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    title?: string
    graphType?: string
    interactive?: boolean
  }
>(({ className, title, graphType, interactive = false, children, ...props }, ref) => (
  <Card
    ref={ref}
    variant={interactive ? "interactive" : "graph"}
    className={cn("relative", className)}
    {...props}
  >
    {title && (
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center justify-between">
          {title}
          {graphType && (
            <span className="text-xs bg-graph-100 dark:bg-graph-900 text-graph-700 dark:text-graph-300 px-2 py-1 rounded">
              {graphType}
            </span>
          )}
        </CardTitle>
      </CardHeader>
    )}
    <CardContent className="p-0">
      <div className="graph-container min-h-[300px] flex items-center justify-center">
        {children}
      </div>
    </CardContent>
  </Card>
))
GraphCard.displayName = "GraphCard"

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
  FormulaCard,
  GraphCard,
  cardVariants,
}
