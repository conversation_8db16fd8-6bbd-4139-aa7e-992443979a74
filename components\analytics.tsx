"use client"

import { useEffect } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

// Google Analytics
declare global {
  interface Window {
    gtag: (command: string, targetId: string, config?: any) => void
  }
}

export function Analytics() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') return

    const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID

    if (!GA_TRACKING_ID) return

    // Load Google Analytics script
    const script = document.createElement('script')
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`
    script.async = true
    document.head.appendChild(script)

    // Initialize gtag
    window.gtag = window.gtag || function() {
      (window as any).dataLayer = (window as any).dataLayer || []
      ;(window as any).dataLayer.push(arguments)
    }

    window.gtag('js', new Date().toISOString())
    window.gtag('config', GA_TRACKING_ID, {
      page_title: document.title,
      page_location: window.location.href,
    })

    return () => {
      document.head.removeChild(script)
    }
  }, [])

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') return
    if (!window.gtag) return

    const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID
    if (!GA_TRACKING_ID) return

    const url = pathname + searchParams.toString()
    
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
      page_title: document.title,
      page_location: window.location.href,
    })
  }, [pathname, searchParams])

  return null
}

// Analytics utility functions
export const analytics = {
  // Track mathematical operations
  trackFormulaGeneration: (data: {
    input: string
    method: 'nlp' | 'direct'
    confidence?: number
    success: boolean
  }) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'formula_generation', {
        event_category: 'mathematical_operations',
        event_label: data.method,
        value: data.confidence ? Math.round(data.confidence * 100) : undefined,
        custom_parameters: {
          input_length: data.input.length,
          success: data.success,
        },
      })
    }
  },

  // Track equation solving
  trackEquationSolving: (data: {
    equation: string
    method: string
    success: boolean
    steps_count?: number
  }) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'equation_solving', {
        event_category: 'mathematical_operations',
        event_label: data.method,
        value: data.steps_count,
        custom_parameters: {
          equation_length: data.equation.length,
          success: data.success,
        },
      })
    }
  },

  // Track visualization usage
  trackVisualization: (data: {
    type: 'graph' | '3d' | 'chart'
    function: string
    success: boolean
  }) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'visualization', {
        event_category: 'mathematical_operations',
        event_label: data.type,
        custom_parameters: {
          function_complexity: data.function.length,
          success: data.success,
        },
      })
    }
  },

  // Track collaboration features
  trackCollaboration: (data: {
    action: 'join_room' | 'share_formula' | 'real_time_edit'
    room_id?: string
    participants?: number
  }) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'collaboration', {
        event_category: 'user_interaction',
        event_label: data.action,
        value: data.participants,
        custom_parameters: {
          room_id: data.room_id,
        },
      })
    }
  },

  // Track user engagement
  trackEngagement: (data: {
    action: 'copy_formula' | 'export_code' | 'save_formula'
    category: string
    value?: number
  }) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', data.action, {
        event_category: data.category,
        value: data.value,
      })
    }
  },

  // Track errors
  trackError: (data: {
    error_type: 'api_error' | 'parsing_error' | 'computation_error'
    error_message: string
    context?: string
  }) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: data.error_message,
        fatal: false,
        custom_parameters: {
          error_type: data.error_type,
          context: data.context,
        },
      })
    }
  },

  // Track performance metrics
  trackPerformance: (data: {
    operation: string
    duration: number
    success: boolean
  }) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'timing_complete', {
        name: data.operation,
        value: data.duration,
        event_category: 'performance',
        custom_parameters: {
          success: data.success,
        },
      })
    }
  },

  // Track feature usage
  trackFeatureUsage: (feature: string, details?: Record<string, any>) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'feature_usage', {
        event_category: 'user_interaction',
        event_label: feature,
        custom_parameters: details,
      })
    }
  },
}

// Performance monitoring hook
export function usePerformanceTracking() {
  const trackOperation = (operation: string, fn: () => Promise<any>) => {
    return async () => {
      const startTime = performance.now()
      let success = false
      
      try {
        const result = await fn()
        success = true
        return result
      } catch (error) {
        analytics.trackError({
          error_type: 'computation_error',
          error_message: error instanceof Error ? error.message : 'Unknown error',
          context: operation,
        })
        throw error
      } finally {
        const duration = performance.now() - startTime
        analytics.trackPerformance({
          operation,
          duration,
          success,
        })
      }
    }
  }

  return { trackOperation }
}
