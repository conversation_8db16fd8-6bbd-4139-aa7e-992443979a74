/**
 * Production configuration for FormulaForge
 */

export interface ProductionConfig {
  app: {
    name: string
    version: string
    environment: string
    port: number
    host: string
    baseUrl: string
  }
  database: {
    url: string
    maxConnections: number
    connectionTimeout: number
    queryTimeout: number
    ssl: boolean
  }
  redis: {
    url: string
    maxRetries: number
    retryDelay: number
  }
  auth: {
    jwtSecret: string
    jwtExpiresIn: string
    refreshTokenExpiresIn: string
    bcryptRounds: number
    sessionTimeout: number
  }
  rateLimit: {
    windowMs: number
    maxRequests: number
    skipSuccessfulRequests: boolean
    skipFailedRequests: boolean
  }
  cors: {
    origin: string[]
    credentials: boolean
    optionsSuccessStatus: number
  }
  security: {
    helmet: {
      contentSecurityPolicy: boolean
      crossOriginEmbedderPolicy: boolean
      crossOriginOpenerPolicy: boolean
      crossOriginResourcePolicy: boolean
      dnsPrefetchControl: boolean
      frameguard: boolean
      hidePoweredBy: boolean
      hsts: boolean
      ieNoOpen: boolean
      noSniff: boolean
      originAgentCluster: boolean
      permittedCrossDomainPolicies: boolean
      referrerPolicy: boolean
      xssFilter: boolean
    }
  }
  logging: {
    level: string
    format: string
    transports: string[]
    maxFiles: number
    maxSize: string
  }
  monitoring: {
    enabled: boolean
    metricsInterval: number
    healthCheckInterval: number
    performanceThresholds: {
      api: number
      database: number
      formula: number
    }
  }
  cache: {
    ttl: number
    maxSize: number
    checkPeriod: number
  }
  fileUpload: {
    maxSize: number
    allowedTypes: string[]
    uploadPath: string
  }
  email: {
    provider: string
    apiKey: string
    fromAddress: string
    templates: {
      welcome: string
      passwordReset: string
      collaboration: string
    }
  }
  ai: {
    openaiApiKey?: string
    maxTokens: number
    temperature: number
    model: string
  }
  collaboration: {
    maxParticipants: number
    sessionTimeout: number
    messageRetention: number
  }
}

function getEnvVar(name: string, defaultValue?: string): string {
  const value = process.env[name]
  if (!value && !defaultValue) {
    throw new Error(`Environment variable ${name} is required`)
  }
  return value || defaultValue!
}

function getEnvNumber(name: string, defaultValue: number): number {
  const value = process.env[name]
  return value ? parseInt(value, 10) : defaultValue
}

function getEnvBoolean(name: string, defaultValue: boolean): boolean {
  const value = process.env[name]
  return value ? value.toLowerCase() === 'true' : defaultValue
}

export const productionConfig: ProductionConfig = {
  app: {
    name: 'FormulaForge',
    version: process.env.npm_package_version || '1.0.0',
    environment: getEnvVar('NODE_ENV', 'production'),
    port: getEnvNumber('PORT', 3000),
    host: getEnvVar('HOST', '0.0.0.0'),
    baseUrl: getEnvVar('NEXT_PUBLIC_APP_URL', 'https://formulaforge.com')
  },

  database: {
    url: getEnvVar('DATABASE_URL'),
    maxConnections: getEnvNumber('DB_MAX_CONNECTIONS', 20),
    connectionTimeout: getEnvNumber('DB_CONNECTION_TIMEOUT', 30000),
    queryTimeout: getEnvNumber('DB_QUERY_TIMEOUT', 10000),
    ssl: getEnvBoolean('DB_SSL', true)
  },

  redis: {
    url: getEnvVar('REDIS_URL', 'redis://localhost:6379'),
    maxRetries: getEnvNumber('REDIS_MAX_RETRIES', 3),
    retryDelay: getEnvNumber('REDIS_RETRY_DELAY', 1000)
  },

  auth: {
    jwtSecret: getEnvVar('JWT_SECRET'),
    jwtExpiresIn: getEnvVar('JWT_EXPIRES_IN', '1h'),
    refreshTokenExpiresIn: getEnvVar('REFRESH_TOKEN_EXPIRES_IN', '7d'),
    bcryptRounds: getEnvNumber('BCRYPT_ROUNDS', 12),
    sessionTimeout: getEnvNumber('SESSION_TIMEOUT', 3600000) // 1 hour
  },

  rateLimit: {
    windowMs: getEnvNumber('RATE_LIMIT_WINDOW_MS', 60000), // 1 minute
    maxRequests: getEnvNumber('RATE_LIMIT_MAX_REQUESTS', 100),
    skipSuccessfulRequests: getEnvBoolean('RATE_LIMIT_SKIP_SUCCESS', false),
    skipFailedRequests: getEnvBoolean('RATE_LIMIT_SKIP_FAILED', false)
  },

  cors: {
    origin: getEnvVar('CORS_ORIGIN', 'https://formulaforge.com').split(','),
    credentials: getEnvBoolean('CORS_CREDENTIALS', true),
    optionsSuccessStatus: getEnvNumber('CORS_OPTIONS_STATUS', 200)
  },

  security: {
    helmet: {
      contentSecurityPolicy: getEnvBoolean('SECURITY_CSP', true),
      crossOriginEmbedderPolicy: getEnvBoolean('SECURITY_COEP', true),
      crossOriginOpenerPolicy: getEnvBoolean('SECURITY_COOP', true),
      crossOriginResourcePolicy: getEnvBoolean('SECURITY_CORP', true),
      dnsPrefetchControl: getEnvBoolean('SECURITY_DNS_PREFETCH', true),
      frameguard: getEnvBoolean('SECURITY_FRAMEGUARD', true),
      hidePoweredBy: getEnvBoolean('SECURITY_HIDE_POWERED_BY', true),
      hsts: getEnvBoolean('SECURITY_HSTS', true),
      ieNoOpen: getEnvBoolean('SECURITY_IE_NO_OPEN', true),
      noSniff: getEnvBoolean('SECURITY_NO_SNIFF', true),
      originAgentCluster: getEnvBoolean('SECURITY_ORIGIN_AGENT_CLUSTER', true),
      permittedCrossDomainPolicies: getEnvBoolean('SECURITY_CROSS_DOMAIN_POLICIES', true),
      referrerPolicy: getEnvBoolean('SECURITY_REFERRER_POLICY', true),
      xssFilter: getEnvBoolean('SECURITY_XSS_FILTER', true)
    }
  },

  logging: {
    level: getEnvVar('LOG_LEVEL', 'info'),
    format: getEnvVar('LOG_FORMAT', 'json'),
    transports: getEnvVar('LOG_TRANSPORTS', 'console,file').split(','),
    maxFiles: getEnvNumber('LOG_MAX_FILES', 5),
    maxSize: getEnvVar('LOG_MAX_SIZE', '10m')
  },

  monitoring: {
    enabled: getEnvBoolean('MONITORING_ENABLED', true),
    metricsInterval: getEnvNumber('METRICS_INTERVAL', 30000), // 30 seconds
    healthCheckInterval: getEnvNumber('HEALTH_CHECK_INTERVAL', 60000), // 1 minute
    performanceThresholds: {
      api: getEnvNumber('PERF_THRESHOLD_API', 1000),
      database: getEnvNumber('PERF_THRESHOLD_DB', 100),
      formula: getEnvNumber('PERF_THRESHOLD_FORMULA', 2000)
    }
  },

  cache: {
    ttl: getEnvNumber('CACHE_TTL', 3600), // 1 hour
    maxSize: getEnvNumber('CACHE_MAX_SIZE', 1000),
    checkPeriod: getEnvNumber('CACHE_CHECK_PERIOD', 600) // 10 minutes
  },

  fileUpload: {
    maxSize: getEnvNumber('UPLOAD_MAX_SIZE', 5 * 1024 * 1024), // 5MB
    allowedTypes: getEnvVar('UPLOAD_ALLOWED_TYPES', 'image/jpeg,image/png,text/plain,application/json').split(','),
    uploadPath: getEnvVar('UPLOAD_PATH', './uploads')
  },

  email: {
    provider: getEnvVar('EMAIL_PROVIDER', 'sendgrid'),
    apiKey: getEnvVar('EMAIL_API_KEY', ''),
    fromAddress: getEnvVar('EMAIL_FROM_ADDRESS', '<EMAIL>'),
    templates: {
      welcome: getEnvVar('EMAIL_TEMPLATE_WELCOME', 'welcome'),
      passwordReset: getEnvVar('EMAIL_TEMPLATE_PASSWORD_RESET', 'password-reset'),
      collaboration: getEnvVar('EMAIL_TEMPLATE_COLLABORATION', 'collaboration-invite')
    }
  },

  ai: {
    openaiApiKey: process.env.OPENAI_API_KEY,
    maxTokens: getEnvNumber('AI_MAX_TOKENS', 1000),
    temperature: parseFloat(getEnvVar('AI_TEMPERATURE', '0.7')),
    model: getEnvVar('AI_MODEL', 'gpt-3.5-turbo')
  },

  collaboration: {
    maxParticipants: getEnvNumber('COLLABORATION_MAX_PARTICIPANTS', 50),
    sessionTimeout: getEnvNumber('COLLABORATION_SESSION_TIMEOUT', 3600000), // 1 hour
    messageRetention: getEnvNumber('COLLABORATION_MESSAGE_RETENTION', **********) // 30 days
  }
}

// Validation function to ensure all required config is present
export function validateConfig(): void {
  const requiredEnvVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ]

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar])
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }

  // Validate JWT secret strength
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long')
  }

  // Validate database URL format
  if (process.env.DATABASE_URL && !process.env.DATABASE_URL.startsWith('postgresql://')) {
    console.warn('DATABASE_URL should start with postgresql:// for PostgreSQL')
  }
}

// Initialize configuration validation in production
if (process.env.NODE_ENV === 'production') {
  try {
    validateConfig()
    console.log('✅ Production configuration validated successfully')
  } catch (error) {
    console.error('❌ Configuration validation failed:', (error as Error).message)
    process.exit(1)
  }
}
