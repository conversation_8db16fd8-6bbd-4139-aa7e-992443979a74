import { NextRequest, NextResponse } from 'next/server'
import { NLPProcessor } from '@/backend/nlp/nlp-processor'
import { MathematicalEngine } from '@/backend/engines/mathematical-engine'
import { rateLimit } from '@/lib/rate-limit'
import OpenAI from 'openai'

// Initialize processors
const nlpProcessor = new NLPProcessor()
const mathEngine = new MathematicalEngine()

// Initialize OpenAI client (optional enhancement)
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null

// Rate limiting
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500,
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 50, 'CACHE_TOKEN') // 50 requests per minute for NLP
    } catch {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { input, useAI = false, context = {} } = body

    // Validate input
    if (!input || typeof input !== 'string') {
      return NextResponse.json(
        { error: 'Input is required and must be a string' },
        { status: 400 }
      )
    }

    if (input.length > 1000) {
      return NextResponse.json(
        { error: 'Input too long. Maximum 1000 characters allowed.' },
        { status: 400 }
      )
    }

    let nlpResult

    // Use AI enhancement if available and requested
    if (useAI && openai) {
      try {
        nlpResult = await processWithAI(input, context)
      } catch (aiError) {
        console.warn('AI processing failed, falling back to rule-based NLP:', aiError)
        nlpResult = await nlpProcessor.processNaturalLanguage(input)
      }
    } else {
      // Use rule-based NLP processor
      nlpResult = await nlpProcessor.processNaturalLanguage(input)
    }

    // If a formula was generated, try to evaluate it
    let evaluationResult = null
    if (nlpResult.formula && nlpResult.confidence > 0.5) {
      try {
        switch (nlpResult.intent) {
          case 'solve':
            if (nlpResult.variables && nlpResult.variables.length > 0) {
              evaluationResult = mathEngine.solveEquation(nlpResult.formula, nlpResult.variables[0])
            } else {
              evaluationResult = mathEngine.evaluate(nlpResult.formula)
            }
            break
          
          case 'derivative':
            const derivVar = nlpResult.variables?.[0] || 'x'
            evaluationResult = mathEngine.calculateDerivative(nlpResult.formula, derivVar)
            break
          
          case 'integral':
            const integVar = nlpResult.variables?.[0] || 'x'
            evaluationResult = mathEngine.calculateIntegral(nlpResult.formula, integVar)
            break
          
          default:
            evaluationResult = mathEngine.evaluate(nlpResult.formula)
        }
      } catch (evalError) {
        console.warn('Formula evaluation failed:', evalError)
        // Continue without evaluation result
      }
    }

    // Generate suggestions for improvement
    const suggestions = generateSuggestions(input, nlpResult)

    // Log successful processing
    console.log(`NLP processing: ${nlpResult.intent} - confidence: ${nlpResult.confidence}`)

    return NextResponse.json({
      success: true,
      input,
      nlp: nlpResult,
      evaluation: evaluationResult,
      suggestions,
      timestamp: new Date().toISOString(),
    })

  } catch (error) {
    console.error('NLP processing error:', error)
    
    return NextResponse.json(
      {
        error: 'Internal server error during NLP processing',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
      { status: 500 }
    )
  }
}

/**
 * Process natural language using OpenAI API
 */
async function processWithAI(input: string, context: any) {
  if (!openai) throw new Error('OpenAI not configured')

  const systemPrompt = `You are a mathematical assistant that converts natural language to mathematical formulas and expressions. 

Your task is to:
1. Identify the mathematical intent (solve, derivative, integral, simplify, etc.)
2. Extract relevant variables and numbers
3. Generate the appropriate mathematical formula or expression
4. Provide a confidence score (0-1)
5. Explain your reasoning

Respond in JSON format with:
{
  "intent": "string",
  "formula": "string",
  "variables": ["array of variables"],
  "confidence": number,
  "explanation": "string"
}

Examples:
- "find the derivative of x squared" → {"intent": "derivative", "formula": "x^2", "variables": ["x"], "confidence": 0.95, "explanation": "Taking derivative of x²"}
- "solve 2x + 5 = 15" → {"intent": "solve", "formula": "2*x + 5 = 15", "variables": ["x"], "confidence": 0.9, "explanation": "Linear equation to solve for x"}
`

  const response = await openai.chat.completions.create({
    model: 'gpt-3.5-turbo',
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: input }
    ],
    temperature: 0.1,
    max_tokens: 500,
  })

  const content = response.choices[0]?.message?.content
  if (!content) throw new Error('No response from AI')

  try {
    const parsed = JSON.parse(content)
    return {
      intent: parsed.intent || 'evaluate',
      entities: {},
      formula: parsed.formula,
      confidence: parsed.confidence || 0.5,
      explanation: parsed.explanation,
      variables: parsed.variables || [],
      operation: parsed.intent || 'evaluate',
    }
  } catch (parseError) {
    throw new Error('Invalid JSON response from AI')
  }
}

/**
 * Generate suggestions for improving the natural language input
 */
function generateSuggestions(input: string, nlpResult: any): string[] {
  const suggestions: string[] = []

  if (nlpResult.confidence < 0.7) {
    suggestions.push('Try being more specific about the mathematical operation you want to perform')
    
    if (!nlpResult.variables || nlpResult.variables.length === 0) {
      suggestions.push('Consider specifying the variables involved (e.g., "solve for x")')
    }
    
    if (!nlpResult.formula) {
      suggestions.push('Try using more mathematical terms like "equation", "derivative", "integral", etc.')
    }
  }

  if (input.length < 10) {
    suggestions.push('Provide more context about what you want to calculate')
  }

  if (!input.match(/\d/)) {
    suggestions.push('Include specific numbers if you want numerical calculations')
  }

  // Intent-specific suggestions
  switch (nlpResult.intent) {
    case 'solve':
      if (!input.includes('=')) {
        suggestions.push('For equation solving, include an equals sign (=)')
      }
      break
    
    case 'derivative':
      if (!input.includes('with respect to') && !input.includes('of')) {
        suggestions.push('Specify what variable to differentiate with respect to')
      }
      break
    
    case 'integral':
      suggestions.push('Specify integration limits if you want a definite integral')
      break
  }

  return suggestions.slice(0, 3) // Limit to 3 suggestions
}

export async function GET(request: NextRequest) {
  // Health check and capabilities endpoint
  return NextResponse.json({
    status: 'healthy',
    service: 'nlp-processor',
    capabilities: [
      'natural-language-to-formula',
      'intent-extraction',
      'variable-detection',
      'pattern-matching',
      'ai-enhancement'
    ],
    ai_available: !!openai,
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  })
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
