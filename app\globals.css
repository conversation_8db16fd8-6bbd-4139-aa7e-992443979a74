@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Theme System */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    
    /* Mathematical interface colors */
    --formula-bg: 199 89% 48% / 0.05;
    --formula-border: 199 89% 48% / 0.2;
    --math-highlight: 84 81% 44% / 0.2;
    --graph-accent: 292 84% 61% / 0.1;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    
    /* Dark mode mathematical colors */
    --formula-bg: 199 89% 48% / 0.1;
    --formula-border: 199 89% 48% / 0.3;
    --math-highlight: 84 81% 44% / 0.3;
    --graph-accent: 292 84% 61% / 0.15;
  }
}

/* Base styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
  
  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
  
  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }
}

/* Mathematical notation styles */
@layer components {
  /* KaTeX overrides for better integration */
  .katex {
    font-size: 1.1em !important;
    line-height: 1.4 !important;
  }
  
  .katex-display {
    margin: 1em 0 !important;
    text-align: center !important;
  }
  
  /* Formula container styles */
  .formula-container {
    @apply relative rounded-lg border p-4 transition-all duration-200;
    background: hsl(var(--formula-bg));
    border-color: hsl(var(--formula-border));
  }
  
  .formula-container:hover {
    @apply shadow-lg;
    border-color: hsl(var(--primary) / 0.4);
  }
  
  .formula-container.active {
    @apply ring-2 ring-primary ring-offset-2;
    border-color: hsl(var(--primary));
  }
  
  /* Math input styles */
  .math-input {
    @apply font-mono text-base leading-relaxed;
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
  }
  
  .math-input:focus {
    @apply outline-none ring-2 ring-primary ring-offset-2;
  }
  
  /* Step-by-step solution styles */
  .solution-step {
    @apply relative pl-8 pb-4 border-l-2 border-muted;
  }
  
  .solution-step::before {
    @apply absolute -left-2 top-0 w-4 h-4 bg-primary rounded-full;
    content: '';
  }
  
  .solution-step:last-child {
    @apply border-l-0;
  }
  
  .solution-step:last-child::after {
    @apply absolute -left-2 top-4 w-4 h-full bg-background;
    content: '';
  }
  
  /* Graph container styles */
  .graph-container {
    @apply relative rounded-xl border overflow-hidden;
    background: hsl(var(--graph-accent));
    border-color: hsl(var(--border));
  }
  
  .graph-container canvas {
    @apply w-full h-full;
  }
  
  /* Code export styles */
  .code-block {
    @apply relative rounded-lg bg-muted p-4 font-mono text-sm;
  }
  
  .code-block pre {
    @apply overflow-x-auto;
  }
  
  .code-block code {
    @apply text-foreground;
  }
  
  /* Collaborative cursor styles */
  .collaborative-cursor {
    @apply absolute pointer-events-none z-50;
    transition: all 0.1s ease-out;
  }
  
  .collaborative-cursor::after {
    @apply absolute top-0 left-0 w-0.5 h-5 bg-current;
    content: '';
  }
  
  .collaborative-cursor::before {
    @apply absolute -top-6 left-0 px-2 py-1 text-xs text-white rounded;
    content: attr(data-user);
    background: currentColor;
  }
  
  /* Loading animations */
  .loading-dots {
    @apply inline-flex space-x-1;
  }
  
  .loading-dots span {
    @apply w-2 h-2 bg-current rounded-full animate-pulse;
    animation-delay: calc(var(--i) * 0.2s);
  }
  
  /* Mathematical highlighting */
  .math-highlight {
    @apply relative;
    background: hsl(var(--math-highlight));
    animation: math-highlight 1s ease-in-out;
  }
  
  /* Responsive mathematical expressions */
  @media (max-width: 640px) {
    .katex {
      font-size: 0.9em !important;
    }
    
    .katex-display {
      margin: 0.5em 0 !important;
    }
    
    .formula-container {
      @apply p-3;
    }
  }
}

/* Utility classes */
@layer utilities {
  /* Mathematical typography */
  .text-math {
    font-family: 'KaTeX_Main', 'Times New Roman', serif;
  }
  
  /* Gradient backgrounds */
  .bg-gradient-formula {
    background: linear-gradient(135deg, hsl(var(--formula-bg)) 0%, hsl(var(--math-highlight)) 100%);
  }
  
  .bg-gradient-graph {
    background: radial-gradient(circle at center, hsl(var(--graph-accent)) 0%, transparent 70%);
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }
  
  .animate-slide-up {
    animation: slide-in-from-bottom 0.3s ease-out;
  }
  
  .animate-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }
  
  /* Layout utilities */
  .container-narrow {
    @apply max-w-4xl mx-auto px-4;
  }
  
  .container-wide {
    @apply max-w-7xl mx-auto px-4;
  }
  
  /* Interactive states */
  .interactive {
    @apply transition-all duration-200 hover:scale-105 active:scale-95;
  }
  
  .interactive-subtle {
    @apply transition-all duration-200 hover:bg-muted/50;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .formula-container {
    @apply border border-gray-300 bg-white text-black;
  }
  
  .katex {
    color: black !important;
  }
}
