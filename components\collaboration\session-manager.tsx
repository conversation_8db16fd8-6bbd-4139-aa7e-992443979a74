'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Users, 
  Plus, 
  Share2, 
  Clock, 
  FileText,
  Settings,
  Trash2,
  Copy,
  ExternalLink
} from 'lucide-react'

interface CollaborationSession {
  id: string
  name: string
  description: string
  createdAt: Date
  lastModified: Date
  participants: Array<{
    id: string
    name: string
    email: string
    role: 'owner' | 'editor' | 'viewer'
    joinedAt: Date
    isActive: boolean
  }>
  isPublic: boolean
  shareLink: string
  documentCount: number
}

interface SessionManagerProps {
  onSessionSelect?: (session: CollaborationSession) => void
  onSessionCreate?: (session: Partial<CollaborationSession>) => void
  onSessionDelete?: (sessionId: string) => void
}

export function SessionManager({
  onSessionSelect,
  onSessionCreate,
  onSessionDelete
}: SessionManagerProps) {
  const [sessions, setSessions] = useState<CollaborationSession[]>([])
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newSessionName, setNewSessionName] = useState('')
  const [newSessionDescription, setNewSessionDescription] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data - in real app, this would come from API
  useEffect(() => {
    const mockSessions: CollaborationSession[] = [
      {
        id: '1',
        name: 'Calculus Problem Set',
        description: 'Working on integration and differentiation problems',
        createdAt: new Date('2024-01-15'),
        lastModified: new Date('2024-01-20'),
        participants: [
          {
            id: '1',
            name: 'John Doe',
            email: '<EMAIL>',
            role: 'owner',
            joinedAt: new Date('2024-01-15'),
            isActive: true
          },
          {
            id: '2',
            name: 'Jane Smith',
            email: '<EMAIL>',
            role: 'editor',
            joinedAt: new Date('2024-01-16'),
            isActive: false
          }
        ],
        isPublic: false,
        shareLink: 'https://formulaforge.com/session/1',
        documentCount: 5
      },
      {
        id: '2',
        name: 'Linear Algebra Study Group',
        description: 'Matrix operations and vector spaces',
        createdAt: new Date('2024-01-10'),
        lastModified: new Date('2024-01-22'),
        participants: [
          {
            id: '1',
            name: 'John Doe',
            email: '<EMAIL>',
            role: 'editor',
            joinedAt: new Date('2024-01-10'),
            isActive: true
          },
          {
            id: '3',
            name: 'Bob Wilson',
            email: '<EMAIL>',
            role: 'owner',
            joinedAt: new Date('2024-01-10'),
            isActive: true
          }
        ],
        isPublic: true,
        shareLink: 'https://formulaforge.com/session/2',
        documentCount: 3
      }
    ]

    setTimeout(() => {
      setSessions(mockSessions)
      setIsLoading(false)
    }, 1000)
  }, [])

  const handleCreateSession = () => {
    if (!newSessionName.trim()) return

    const newSession: CollaborationSession = {
      id: Date.now().toString(),
      name: newSessionName,
      description: newSessionDescription,
      createdAt: new Date(),
      lastModified: new Date(),
      participants: [
        {
          id: 'current-user',
          name: 'Current User',
          email: '<EMAIL>',
          role: 'owner',
          joinedAt: new Date(),
          isActive: true
        }
      ],
      isPublic: false,
      shareLink: `https://formulaforge.com/session/${Date.now()}`,
      documentCount: 0
    }

    setSessions(prev => [newSession, ...prev])
    onSessionCreate?.(newSession)
    
    setNewSessionName('')
    setNewSessionDescription('')
    setIsCreateDialogOpen(false)
  }

  const handleDeleteSession = (sessionId: string) => {
    setSessions(prev => prev.filter(s => s.id !== sessionId))
    onSessionDelete?.(sessionId)
  }

  const copyShareLink = (shareLink: string) => {
    navigator.clipboard.writeText(shareLink)
    // In a real app, you'd show a toast notification here
    console.log('Share link copied to clipboard')
  }

  const getActiveParticipants = (session: CollaborationSession) => {
    return session.participants.filter(p => p.isActive).length
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Collaboration Sessions</h2>
          <p className="text-muted-foreground">
            Manage your collaborative mathematical workspaces
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              New Session
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Collaboration Session</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Session Name</label>
                <Input
                  value={newSessionName}
                  onChange={(e) => setNewSessionName(e.target.value)}
                  placeholder="Enter session name..."
                />
              </div>
              <div>
                <label className="text-sm font-medium">Description (Optional)</label>
                <Input
                  value={newSessionDescription}
                  onChange={(e) => setNewSessionDescription(e.target.value)}
                  placeholder="Describe what you'll be working on..."
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateSession}>
                  Create Session
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Sessions Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {sessions.map((session) => (
          <Card key={session.id} className="hover:shadow-lg transition-shadow cursor-pointer">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg line-clamp-1">{session.name}</CardTitle>
                  {session.description && (
                    <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                      {session.description}
                    </p>
                  )}
                </div>
                <div className="flex gap-1">
                  {session.isPublic && (
                    <Badge variant="secondary" className="text-xs">
                      Public
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Participants */}
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">
                  {getActiveParticipants(session)} active, {session.participants.length} total
                </span>
              </div>

              {/* Documents */}
              <div className="flex items-center gap-2">
                <FileText className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">{session.documentCount} documents</span>
              </div>

              {/* Last Modified */}
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm">
                  Modified {session.lastModified.toLocaleDateString()}
                </span>
              </div>

              {/* Participant Avatars */}
              <div className="flex items-center gap-2">
                <div className="flex -space-x-2">
                  {session.participants.slice(0, 3).map((participant) => (
                    <div
                      key={participant.id}
                      className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-medium border-2 border-background"
                      title={participant.name}
                    >
                      {participant.name.charAt(0).toUpperCase()}
                    </div>
                  ))}
                  {session.participants.length > 3 && (
                    <div className="w-8 h-8 rounded-full bg-muted text-muted-foreground flex items-center justify-center text-xs font-medium border-2 border-background">
                      +{session.participants.length - 3}
                    </div>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-2">
                <Button 
                  size="sm" 
                  className="flex-1"
                  onClick={() => onSessionSelect?.(session)}
                >
                  <ExternalLink className="w-4 h-4 mr-1" />
                  Open
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => copyShareLink(session.shareLink)}
                >
                  <Copy className="w-4 h-4" />
                </Button>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleDeleteSession(session.id)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {sessions.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No collaboration sessions yet</h3>
          <p className="text-muted-foreground mb-4">
            Create your first session to start collaborating on mathematical problems
          </p>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Create Your First Session
          </Button>
        </div>
      )}
    </div>
  )
}
