/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProject%5CAI%5CFormulaForge%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CAI%5CFormulaForge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProject%5CAI%5CFormulaForge%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CAI%5CFormulaForge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZfbm90LWZvdW5kJTJGcGFnZSZwYWdlPSUyRl9ub3QtZm91bmQlMkZwYWdlJmFwcFBhdGhzPSZwYWdlUGF0aD1ub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZub3QtZm91bmQtZXJyb3IuanMmYXBwRGlyPUQlM0ElNUNQcm9qZWN0JTVDQUklNUNGb3JtdWxhRm9yZ2UlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNQcm9qZWN0JTVDQUklNUNGb3JtdWxhRm9yZ2UmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQztBQUNyQztBQUNBLHNCQUFzQiwwTkFBZ0Y7QUFDdEc7QUFDQTtBQUNBLGFBQWE7QUFDYixXQUFXLElBQUk7QUFDZixTQUFTO0FBQ1Q7QUFDQSx5QkFBeUIsNElBQW1GO0FBQzVHLG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JtdWxhZm9yZ2UvP2FmYzgiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICAgIGNoaWxkcmVuOiBbXCIvX25vdC1mb3VuZFwiLCB7XG4gICAgICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgICAgIHBhZ2U6IFtcbiAgICAgICAgICAgICAgICAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksXG4gICAgICAgICAgICAgICAgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJcbiAgICAgICAgICAgICAgXVxuICAgICAgICAgICAgfV1cbiAgICAgICAgICB9LCB7fV1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdFxcXFxBSVxcXFxGb3JtdWxhRm9yZ2VcXFxcYXBwXFxcXGxheW91dC50c3hcIiksIFwiRDpcXFxcUHJvamVjdFxcXFxBSVxcXFxGb3JtdWxhRm9yZ2VcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvX25vdC1mb3VuZC9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL19ub3QtZm91bmQvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvX25vdC1mb3VuZFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProject%5CAI%5CFormulaForge%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CAI%5CFormulaForge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Canalytics.tsx%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Canalytics.tsx%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/analytics.tsx */ \"(ssr)/./components/analytics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/auth-provider.tsx */ \"(ssr)/./components/auth-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/socket-provider.tsx */ \"(ssr)/./components/socket-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Canalytics.tsx%22%2C%22ids%22%3A%5B%22Analytics%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Cauth-provider.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Csocket-provider.tsx%22%2C%22ids%22%3A%5B%22SocketProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Ccomponents%5C%5Cui%5C%5Ctoaster.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-sans%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProject%5C%5CAI%5C%5CFormulaForge%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/analytics.tsx":
/*!**********************************!*\
  !*** ./components/analytics.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics),\n/* harmony export */   analytics: () => (/* binding */ analytics),\n/* harmony export */   usePerformanceTracking: () => (/* binding */ usePerformanceTracking)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics,analytics,usePerformanceTracking auto */ \n\nfunction Analytics() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (true) return;\n        const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;\n        if (!GA_TRACKING_ID) return;\n        // Load Google Analytics script\n        const script = document.createElement(\"script\");\n        script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`;\n        script.async = true;\n        document.head.appendChild(script);\n        // Initialize gtag\n        window.gtag = window.gtag || function() {\n            window.dataLayer = window.dataLayer || [];\n            window.dataLayer.push(arguments);\n        };\n        window.gtag(\"js\", new Date().toISOString());\n        window.gtag(\"config\", GA_TRACKING_ID, {\n            page_title: document.title,\n            page_location: window.location.href\n        });\n        return ()=>{\n            document.head.removeChild(script);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (true) return;\n        if (!window.gtag) return;\n        const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID;\n        if (!GA_TRACKING_ID) return;\n        const url = pathname + searchParams.toString();\n        window.gtag(\"config\", GA_TRACKING_ID, {\n            page_path: url,\n            page_title: document.title,\n            page_location: window.location.href\n        });\n    }, [\n        pathname,\n        searchParams\n    ]);\n    return null;\n}\n// Analytics utility functions\nconst analytics = {\n    // Track mathematical operations\n    trackFormulaGeneration: (data)=>{\n        if (false) {}\n    },\n    // Track equation solving\n    trackEquationSolving: (data)=>{\n        if (false) {}\n    },\n    // Track visualization usage\n    trackVisualization: (data)=>{\n        if (false) {}\n    },\n    // Track collaboration features\n    trackCollaboration: (data)=>{\n        if (false) {}\n    },\n    // Track user engagement\n    trackEngagement: (data)=>{\n        if (false) {}\n    },\n    // Track errors\n    trackError: (data)=>{\n        if (false) {}\n    },\n    // Track performance metrics\n    trackPerformance: (data)=>{\n        if (false) {}\n    },\n    // Track feature usage\n    trackFeatureUsage: (feature, details)=>{\n        if (false) {}\n    }\n};\n// Performance monitoring hook\nfunction usePerformanceTracking() {\n    const trackOperation = (operation, fn)=>{\n        return async ()=>{\n            const startTime = performance.now();\n            let success = false;\n            try {\n                const result = await fn();\n                success = true;\n                return result;\n            } catch (error) {\n                analytics.trackError({\n                    error_type: \"computation_error\",\n                    error_message: error instanceof Error ? error.message : \"Unknown error\",\n                    context: operation\n                });\n                throw error;\n            } finally{\n                const duration = performance.now() - startTime;\n                analytics.trackPerformance({\n                    operation,\n                    duration,\n                    success\n                });\n            }\n        };\n    };\n    return {\n        trackOperation\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/analytics.tsx\n");

/***/ }),

/***/ "(ssr)/./components/auth-provider.tsx":
/*!**************************************!*\
  !*** ./components/auth-provider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_2__.createBrowserClient)(\"https://placeholder.supabase.co\", \"placeholder_anon_key\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get initial session\n        const getInitialSession = async ()=>{\n            const { data: { session } } = await supabase.auth.getSession();\n            setSession(session);\n            setUser(session?.user ?? null);\n            setLoading(false);\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            setSession(session);\n            setUser(session?.user ?? null);\n            setLoading(false);\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        supabase.auth\n    ]);\n    const signIn = async (email, password)=>{\n        const { data, error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const signUp = async (email, password)=>{\n        const { data, error } = await supabase.auth.signUp({\n            email,\n            password\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const signOut = async ()=>{\n        await supabase.auth.signOut();\n    };\n    const resetPassword = async (email)=>{\n        const { data, error } = await supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n        return {\n            data,\n            error\n        };\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        resetPassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\auth-provider.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/auth-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/socket-provider.tsx":
/*!****************************************!*\
  !*** ./components/socket-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketProvider: () => (/* binding */ SocketProvider),\n/* harmony export */   useCollaboration: () => (/* binding */ useCollaboration),\n/* harmony export */   useRealtimeFormula: () => (/* binding */ useRealtimeFormula),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* __next_internal_client_entry_do_not_use__ SocketProvider,useSocket,useCollaboration,useRealtimeFormula auto */ \n\n\nconst SocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction SocketProvider({ children }) {\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize socket connection\n        const socketInstance = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_2__.io)(process.env.NEXT_PUBLIC_SOCKET_URL || \"\", {\n            path: \"/api/socket\",\n            addTrailingSlash: false\n        });\n        // Connection event handlers\n        socketInstance.on(\"connect\", ()=>{\n            console.log(\"Socket connected:\", socketInstance.id);\n            setIsConnected(true);\n        });\n        socketInstance.on(\"disconnect\", ()=>{\n            console.log(\"Socket disconnected\");\n            setIsConnected(false);\n        });\n        socketInstance.on(\"connect_error\", (error)=>{\n            console.error(\"Socket connection error:\", error);\n            setIsConnected(false);\n        });\n        // Mathematical collaboration events\n        socketInstance.on(\"formula_updated\", (data)=>{\n            console.log(\"Formula updated:\", data);\n        // Handle real-time formula updates\n        });\n        socketInstance.on(\"user_joined\", (data)=>{\n            console.log(\"User joined:\", data);\n        // Handle user joining collaboration session\n        });\n        socketInstance.on(\"user_left\", (data)=>{\n            console.log(\"User left:\", data);\n        // Handle user leaving collaboration session\n        });\n        socketInstance.on(\"cursor_moved\", (data)=>{\n            console.log(\"Cursor moved:\", data);\n        // Handle real-time cursor movements\n        });\n        setSocket(socketInstance);\n        // Cleanup on unmount\n        return ()=>{\n            socketInstance.close();\n        };\n    }, []);\n    const joinRoom = (roomId)=>{\n        if (socket) {\n            socket.emit(\"join_room\", {\n                roomId\n            });\n            console.log(`Joined room: ${roomId}`);\n        }\n    };\n    const leaveRoom = (roomId)=>{\n        if (socket) {\n            socket.emit(\"leave_room\", {\n                roomId\n            });\n            console.log(`Left room: ${roomId}`);\n        }\n    };\n    const sendMessage = (event, data)=>{\n        if (socket && isConnected) {\n            socket.emit(event, data);\n        } else {\n            console.warn(\"Socket not connected, message not sent:\", event, data);\n        }\n    };\n    const value = {\n        socket,\n        isConnected,\n        joinRoom,\n        leaveRoom,\n        sendMessage\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SocketContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\socket-provider.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\nfunction useSocket() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SocketContext);\n    if (context === undefined) {\n        throw new Error(\"useSocket must be used within a SocketProvider\");\n    }\n    return context;\n}\n// Custom hooks for specific socket functionality\nfunction useCollaboration(roomId) {\n    const { socket, isConnected, joinRoom, leaveRoom, sendMessage } = useSocket();\n    const [collaborators, setCollaborators] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isInRoom, setIsInRoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!socket || !roomId) return;\n        // Join room when component mounts\n        joinRoom(roomId);\n        setIsInRoom(true);\n        // Listen for collaborator updates\n        socket.on(\"collaborators_updated\", (data)=>{\n            setCollaborators(data.collaborators);\n        });\n        // Cleanup when component unmounts or roomId changes\n        return ()=>{\n            if (isInRoom) {\n                leaveRoom(roomId);\n                setIsInRoom(false);\n            }\n            socket.off(\"collaborators_updated\");\n        };\n    }, [\n        socket,\n        roomId,\n        joinRoom,\n        leaveRoom,\n        isInRoom\n    ]);\n    const shareFormula = (formula, metadata)=>{\n        sendMessage(\"share_formula\", {\n            roomId,\n            formula,\n            metadata,\n            timestamp: Date.now()\n        });\n    };\n    const updateCursor = (position)=>{\n        sendMessage(\"cursor_update\", {\n            roomId,\n            position,\n            timestamp: Date.now()\n        });\n    };\n    const sendChat = (message)=>{\n        sendMessage(\"chat_message\", {\n            roomId,\n            message,\n            timestamp: Date.now()\n        });\n    };\n    return {\n        collaborators,\n        isInRoom,\n        shareFormula,\n        updateCursor,\n        sendChat,\n        isConnected\n    };\n}\nfunction useRealtimeFormula() {\n    const { socket } = useSocket();\n    const [sharedFormulas, setSharedFormulas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!socket) return;\n        socket.on(\"formula_shared\", (data)=>{\n            setSharedFormulas((prev)=>[\n                    ...prev,\n                    data\n                ]);\n        });\n        socket.on(\"formula_updated\", (data)=>{\n            setSharedFormulas((prev)=>prev.map((formula)=>formula.id === data.id ? {\n                        ...formula,\n                        ...data\n                    } : formula));\n        });\n        return ()=>{\n            socket.off(\"formula_shared\");\n            socket.off(\"formula_updated\");\n        };\n    }, [\n        socket\n    ]);\n    return {\n        sharedFormulas,\n        setSharedFormulas\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/socket-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.module.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRzFELFNBQVNDLGNBQWMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQTJCO0lBQ3RFLHFCQUFPLDhEQUFDRixzREFBa0JBO1FBQUUsR0FBR0UsS0FBSztrQkFBR0Q7Ozs7OztBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvcm11bGFmb3JnZS8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB7IHR5cGUgVGhlbWVQcm92aWRlclByb3BzIH0gZnJvbSBcIm5leHQtdGhlbWVzL2Rpc3QvdHlwZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\",\n            success: \"border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-50\",\n            warning: \"border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-50\",\n            info: \"border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-50\",\n            // Mathematical interface variants\n            formula: \"border-formula-200 bg-formula-50 text-formula-900 dark:border-formula-800 dark:bg-formula-950 dark:text-formula-50\",\n            math: \"border-math-200 bg-math-50 text-math-900 dark:border-math-800 dark:bg-math-950 dark:text-math-50\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toast.tsx\",\n            lineNumber: 95,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 86,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 104,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toast.tsx\",\n        lineNumber: 116,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-toast */ \"(ssr)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(function({ id, title, description, action, ...props }) {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toaster.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toaster.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toaster.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this);\n            }),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toaster.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\components\\\\ui\\\\toaster.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RvYXN0ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQVM4QjtBQUNjO0FBRXJDLFNBQVNPO0lBQ2QsTUFBTSxFQUFFQyxNQUFNLEVBQUUsR0FBR0YsMERBQVFBO0lBRTNCLHFCQUNFLDhEQUFDSCwrREFBYUE7O1lBQ1hLLE9BQU9DLEdBQUcsQ0FBQyxTQUFVLEVBQUVDLEVBQUUsRUFBRUMsS0FBSyxFQUFFQyxXQUFXLEVBQUVDLE1BQU0sRUFBRSxHQUFHQyxPQUFPO2dCQUNoRSxxQkFDRSw4REFBQ2QsdURBQUtBO29CQUFXLEdBQUdjLEtBQUs7O3NDQUN2Qiw4REFBQ0M7NEJBQUlDLFdBQVU7O2dDQUNaTCx1QkFBUyw4REFBQ1AsNERBQVVBOzhDQUFFTzs7Ozs7O2dDQUN0QkMsNkJBQ0MsOERBQUNWLGtFQUFnQkE7OENBQUVVOzs7Ozs7Ozs7Ozs7d0JBR3RCQztzQ0FDRCw4REFBQ1osNERBQVVBOzs7Ozs7bUJBUkRTOzs7OztZQVdoQjswQkFDQSw4REFBQ0wsK0RBQWFBOzs7Ozs7Ozs7OztBQUdwQiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvcm11bGFmb3JnZS8uL2NvbXBvbmVudHMvdWkvdG9hc3Rlci50c3g/MzZlNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQge1xuICBUb2FzdCxcbiAgVG9hc3RDbG9zZSxcbiAgVG9hc3REZXNjcmlwdGlvbixcbiAgVG9hc3RQcm92aWRlcixcbiAgVG9hc3RUaXRsZSxcbiAgVG9hc3RWaWV3cG9ydCxcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90b2FzdFwiXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gXCJAL2hvb2tzL3VzZS10b2FzdFwiXG5cbmV4cG9ydCBmdW5jdGlvbiBUb2FzdGVyKCkge1xuICBjb25zdCB7IHRvYXN0cyB9ID0gdXNlVG9hc3QoKVxuXG4gIHJldHVybiAoXG4gICAgPFRvYXN0UHJvdmlkZXI+XG4gICAgICB7dG9hc3RzLm1hcChmdW5jdGlvbiAoeyBpZCwgdGl0bGUsIGRlc2NyaXB0aW9uLCBhY3Rpb24sIC4uLnByb3BzIH0pIHtcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8VG9hc3Qga2V5PXtpZH0gey4uLnByb3BzfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtMVwiPlxuICAgICAgICAgICAgICB7dGl0bGUgJiYgPFRvYXN0VGl0bGU+e3RpdGxlfTwvVG9hc3RUaXRsZT59XG4gICAgICAgICAgICAgIHtkZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgICAgPFRvYXN0RGVzY3JpcHRpb24+e2Rlc2NyaXB0aW9ufTwvVG9hc3REZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAge2FjdGlvbn1cbiAgICAgICAgICAgIDxUb2FzdENsb3NlIC8+XG4gICAgICAgICAgPC9Ub2FzdD5cbiAgICAgICAgKVxuICAgICAgfSl9XG4gICAgICA8VG9hc3RWaWV3cG9ydCAvPlxuICAgIDwvVG9hc3RQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlRvYXN0IiwiVG9hc3RDbG9zZSIsIlRvYXN0RGVzY3JpcHRpb24iLCJUb2FzdFByb3ZpZGVyIiwiVG9hc3RUaXRsZSIsIlRvYXN0Vmlld3BvcnQiLCJ1c2VUb2FzdCIsIlRvYXN0ZXIiLCJ0b2FzdHMiLCJtYXAiLCJpZCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJhY3Rpb24iLCJwcm9wcyIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ \nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./hooks/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   escapeLatex: () => (/* binding */ escapeLatex),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getRelativeTime: () => (/* binding */ getRelativeTime),\n/* harmony export */   isClient: () => (/* binding */ isClient),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidMathExpression: () => (/* binding */ isValidMathExpression),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   stringToColor: () => (/* binding */ stringToColor),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   toLatex: () => (/* binding */ toLatex)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes\n * Combines clsx for conditional classes and tailwind-merge for deduplication\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format numbers for display in mathematical contexts\n */ function formatNumber(num, options = {}) {\n    const { precision = 6, notation = \"standard\", compact = false } = options;\n    if (!isFinite(num)) {\n        if (isNaN(num)) return \"NaN\";\n        return num > 0 ? \"∞\" : \"-∞\";\n    }\n    if (compact && Math.abs(num) >= 1000) {\n        return new Intl.NumberFormat(\"en-US\", {\n            notation: \"compact\",\n            maximumFractionDigits: 2\n        }).format(num);\n    }\n    switch(notation){\n        case \"scientific\":\n            return num.toExponential(precision);\n        case \"engineering\":\n            const exp = Math.floor(Math.log10(Math.abs(num)) / 3) * 3;\n            const mantissa = num / Math.pow(10, exp);\n            return `${mantissa.toFixed(precision)}e${exp}`;\n        default:\n            return parseFloat(num.toPrecision(precision)).toString();\n    }\n}\n/**\n * Debounce function for performance optimization\n */ function debounce(func, wait) {\n    let timeout = null;\n    return (...args)=>{\n        if (timeout) clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Throttle function for performance optimization\n */ function throttle(func, limit) {\n    let inThrottle = false;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * Generate a unique ID for mathematical expressions\n */ function generateId(prefix = \"id\") {\n    return `${prefix}-${Math.random().toString(36).substr(2, 9)}-${Date.now().toString(36)}`;\n}\n/**\n * Validate mathematical expression syntax\n */ function isValidMathExpression(expression) {\n    try {\n        // Basic validation - check for balanced parentheses\n        let balance = 0;\n        for (const char of expression){\n            if (char === \"(\") balance++;\n            if (char === \")\") balance--;\n            if (balance < 0) return false;\n        }\n        if (balance !== 0) return false;\n        // Check for invalid characters (basic validation)\n        const validChars = /^[0-9+\\-*/().\\s\\w^√∫∑∏πe,=<>!|&]+$/;\n        if (!validChars.test(expression)) return false;\n        return true;\n    } catch  {\n        return false;\n    }\n}\n/**\n * Escape special characters for LaTeX rendering\n */ function escapeLatex(text) {\n    return text.replace(/\\\\/g, \"\\\\\\\\\").replace(/\\{/g, \"\\\\{\").replace(/\\}/g, \"\\\\}\").replace(/\\$/g, \"\\\\$\").replace(/&/g, \"\\\\&\").replace(/%/g, \"\\\\%\").replace(/#/g, \"\\\\#\").replace(/\\^/g, \"\\\\^{}\").replace(/_/g, \"\\\\_\").replace(/~/g, \"\\\\~{}\");\n}\n/**\n * Convert mathematical expression to LaTeX format\n */ function toLatex(expression) {\n    return expression// Replace common mathematical symbols\n    .replace(/\\*/g, \" \\\\cdot \").replace(/sqrt\\(([^)]+)\\)/g, \"\\\\sqrt{$1}\").replace(/\\^([0-9]+)/g, \"^{$1}\").replace(/\\^([a-zA-Z]+)/g, \"^{$1}\").replace(/([0-9]+)\\/([0-9]+)/g, \"\\\\frac{$1}{$2}\").replace(/pi/g, \"\\\\pi\").replace(/infinity/g, \"\\\\infty\").replace(/alpha/g, \"\\\\alpha\").replace(/beta/g, \"\\\\beta\").replace(/gamma/g, \"\\\\gamma\").replace(/delta/g, \"\\\\delta\").replace(/theta/g, \"\\\\theta\").replace(/lambda/g, \"\\\\lambda\").replace(/mu/g, \"\\\\mu\").replace(/sigma/g, \"\\\\sigma\").replace(/phi/g, \"\\\\phi\").replace(/omega/g, \"\\\\omega\");\n}\n/**\n * Copy text to clipboard with fallback\n */ async function copyToClipboard(text) {\n    try {\n        if (navigator.clipboard && window.isSecureContext) {\n            await navigator.clipboard.writeText(text);\n            return true;\n        } else {\n            // Fallback for older browsers\n            const textArea = document.createElement(\"textarea\");\n            textArea.value = text;\n            textArea.style.position = \"fixed\";\n            textArea.style.left = \"-999999px\";\n            textArea.style.top = \"-999999px\";\n            document.body.appendChild(textArea);\n            textArea.focus();\n            textArea.select();\n            const success = document.execCommand(\"copy\");\n            document.body.removeChild(textArea);\n            return success;\n        }\n    } catch (error) {\n        console.error(\"Failed to copy to clipboard:\", error);\n        return false;\n    }\n}\n/**\n * Format file size for display\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n/**\n * Get relative time string\n */ function getRelativeTime(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return \"just now\";\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`;\n    return date.toLocaleDateString();\n}\n/**\n * Validate email address\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Generate color from string (for user avatars, etc.)\n */ function stringToColor(str) {\n    let hash = 0;\n    for(let i = 0; i < str.length; i++){\n        hash = str.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    const hue = hash % 360;\n    return `hsl(${hue}, 70%, 50%)`;\n}\n/**\n * Sleep utility for async operations\n */ function sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n/**\n * Check if code is running on client side\n */ function isClient() {\n    return \"undefined\" !== \"undefined\";\n}\n/**\n * Safe JSON parse with fallback\n */ function safeJsonParse(json, fallback) {\n    try {\n        return JSON.parse(json);\n    } catch  {\n        return fallback;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"551f17d899ae\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb3JtdWxhZm9yZ2UvLi9hcHAvZ2xvYmFscy5jc3M/ODFlMSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjU1MWYxN2Q4OTlhZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-sans\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-sans\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-mono\",\"display\":\"swap\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-mono\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n/* harmony import */ var _components_auth_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth-provider */ \"(rsc)/./components/auth-provider.tsx\");\n/* harmony import */ var _components_socket_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/socket-provider */ \"(rsc)/./components/socket-provider.tsx\");\n/* harmony import */ var _components_analytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/analytics */ \"(rsc)/./components/analytics.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"FormulaForge - Advanced Mathematical Formula Generator\",\n        template: \"%s | FormulaForge\"\n    },\n    description: \"The ultimate mathematical formula generation and manipulation platform. Generate formulas from natural language, solve equations, visualize functions, and collaborate in real-time.\",\n    keywords: [\n        \"mathematics\",\n        \"formulas\",\n        \"equations\",\n        \"calculator\",\n        \"symbolic math\",\n        \"AI\",\n        \"natural language processing\",\n        \"visualization\",\n        \"collaboration\",\n        \"calculus\",\n        \"algebra\",\n        \"statistics\"\n    ],\n    authors: [\n        {\n            name: \"HectorTa1989\",\n            url: \"https://github.com/HectorTa1989\"\n        }\n    ],\n    creator: \"HectorTa1989\",\n    publisher: \"FormulaForge\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"http://localhost:3000\" || 0),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"FormulaForge - Advanced Mathematical Formula Generator\",\n        description: \"Generate and manipulate mathematical formulas with AI-powered natural language processing.\",\n        siteName: \"FormulaForge\",\n        images: [\n            {\n                url: \"/og-image.png\",\n                width: 1200,\n                height: 630,\n                alt: \"FormulaForge - Mathematical Formula Generator\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"FormulaForge - Advanced Mathematical Formula Generator\",\n        description: \"Generate and manipulate mathematical formulas with AI-powered natural language processing.\",\n        images: [\n            \"/og-image.png\"\n        ],\n        creator: \"@HectorTa1989\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: process.env.GOOGLE_SITE_VERIFICATION,\n        yandex: process.env.YANDEX_VERIFICATION,\n        yahoo: process.env.YAHOO_VERIFICATION\n    },\n    category: \"technology\",\n    classification: \"Educational Software\",\n    referrer: \"origin-when-cross-origin\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preload\",\n                        href: \"/fonts/KaTeX_Main-Regular.woff2\",\n                        as: \"font\",\n                        type: \"font/woff2\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"stylesheet\",\n                        href: \"https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css\",\n                        integrity: \"sha384-n8MVd4RsNIU0tAv4ct0nTaAbDJwPJzDEaqSD1odI+WdtXRGWt2kTvGFasHpSy3SV\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#0ea5e9\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://cdn.jsdelivr.net\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//api.openai.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//api.wolframalpha.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_sans_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_variable_font_mono_display_swap_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_8___default().variable)} font-sans antialiased min-h-screen bg-background text-foreground`,\n                suppressHydrationWarning: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n                        attribute: \"class\",\n                        defaultTheme: \"system\",\n                        enableSystem: true,\n                        disableTransitionOnChange: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_provider__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_socket_provider__WEBPACK_IMPORTED_MODULE_4__.SocketProvider, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative flex min-h-screen flex-col\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: children\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics__WEBPACK_IMPORTED_MODULE_5__.Analytics, {}, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n              if ('serviceWorker' in navigator) {\n                window.addEventListener('load', function() {\n                  navigator.serviceWorker.register('/sw.js')\n                    .then(function(registration) {\n                      console.log('SW registered: ', registration);\n                    })\n                    .catch(function(registrationError) {\n                      console.log('SW registration failed: ', registrationError);\n                    });\n                });\n              }\n            `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Project\\\\AI\\\\FormulaForge\\\\app\\\\layout.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/analytics.tsx":
/*!**********************************!*\
  !*** ./components/analytics.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Analytics: () => (/* binding */ e0),
/* harmony export */   analytics: () => (/* binding */ e1),
/* harmony export */   usePerformanceTracking: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\analytics.tsx#Analytics`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\analytics.tsx#analytics`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\analytics.tsx#usePerformanceTracking`);


/***/ }),

/***/ "(rsc)/./components/auth-provider.tsx":
/*!**************************************!*\
  !*** ./components/auth-provider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\auth-provider.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\auth-provider.tsx#useAuth`);


/***/ }),

/***/ "(rsc)/./components/socket-provider.tsx":
/*!****************************************!*\
  !*** ./components/socket-provider.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SocketProvider: () => (/* binding */ e0),
/* harmony export */   useCollaboration: () => (/* binding */ e2),
/* harmony export */   useRealtimeFormula: () => (/* binding */ e3),
/* harmony export */   useSocket: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\socket-provider.tsx#SocketProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\socket-provider.tsx#useSocket`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\socket-provider.tsx#useCollaboration`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\socket-provider.tsx#useRealtimeFormula`);


/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Project\AI\FormulaForge\components\ui\toaster.tsx#Toaster`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/engine.io-client","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/ws","vendor-chunks/socket.io-client","vendor-chunks/whatwg-url","vendor-chunks/lucide-react","vendor-chunks/socket.io-parser","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/engine.io-parser","vendor-chunks/ramda","vendor-chunks/class-variance-authority","vendor-chunks/cookie","vendor-chunks/next-themes","vendor-chunks/@socket.io","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@swc","vendor-chunks/isows","vendor-chunks/clsx","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CProject%5CAI%5CFormulaForge%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProject%5CAI%5CFormulaForge&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();