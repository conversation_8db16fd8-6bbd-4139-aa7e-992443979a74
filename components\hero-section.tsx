'use client'

import React from 'react'
import Link from 'next/link'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Calculator } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'

export function HeroSection() {
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-muted/20">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      {/* Floating Elements */}
      <div className="absolute top-20 left-10 animate-float">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
      </div>
      <div className="absolute top-40 right-20 animate-float-delayed">
        <div className="w-24 h-24 bg-gradient-to-br from-green-500/20 to-blue-500/20 rounded-full blur-xl" />
      </div>
      <div className="absolute bottom-20 left-1/4 animate-float">
        <div className="w-20 h-20 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-xl" />
      </div>

      <div className="container-wide relative">
        <div className="flex flex-col items-center justify-center min-h-[80vh] text-center space-y-8 py-20">
          {/* Announcement Badge */}
          <Badge variant="outline" className="px-4 py-2 text-sm">
            <Sparkles className="w-4 h-4 mr-2" />
            Introducing AI-Powered Formula Generation
          </Badge>

          {/* Main Heading */}
          <div className="space-y-4 max-w-4xl">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight">
              Transform Ideas into{' '}
              <span className="bg-gradient-to-r from-primary via-blue-600 to-purple-600 bg-clip-text text-transparent">
                Mathematical Formulas
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Generate, solve, and visualize complex mathematical expressions using natural language. 
              Powered by advanced AI and real-time collaboration.
            </p>
          </div>

          {/* Feature Highlights */}
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <div className="flex items-center gap-2 px-3 py-1 bg-muted/50 rounded-full">
              <Brain className="w-4 h-4 text-blue-500" />
              <span>AI-Powered NLP</span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1 bg-muted/50 rounded-full">
              <Calculator className="w-4 h-4 text-green-500" />
              <span>Step-by-Step Solutions</span>
            </div>
            <div className="flex items-center gap-2 px-3 py-1 bg-muted/50 rounded-full">
              <Zap className="w-4 h-4 text-yellow-500" />
              <span>Real-time Collaboration</span>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 pt-4">
            <Button size="lg" className="text-lg px-8 py-6" asChild>
              <Link href="/app">
                Start Creating Formulas
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6" asChild>
              <Link href="/demo">
                <Play className="mr-2 w-5 h-5" />
                Watch Demo
              </Link>
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 pt-12 w-full max-w-2xl">
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-primary">1M+</div>
              <div className="text-sm text-muted-foreground">Formulas Generated</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-primary">50K+</div>
              <div className="text-sm text-muted-foreground">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-primary">500+</div>
              <div className="text-sm text-muted-foreground">Math Operations</div>
            </div>
            <div className="text-center">
              <div className="text-2xl md:text-3xl font-bold text-primary">99.9%</div>
              <div className="text-sm text-muted-foreground">Accuracy Rate</div>
            </div>
          </div>

          {/* Demo Preview */}
          <div className="pt-12 w-full max-w-4xl">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-purple-500/20 rounded-2xl blur-3xl" />
              <div className="relative bg-background/80 backdrop-blur-sm border rounded-2xl p-8 shadow-2xl">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full" />
                    <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                    <div className="w-3 h-3 bg-green-500 rounded-full" />
                    <span className="text-sm text-muted-foreground ml-4">FormulaForge</span>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="text-left">
                      <div className="text-sm text-muted-foreground mb-1">Natural Language Input:</div>
                      <div className="bg-muted/50 rounded-lg p-3 text-sm font-mono">
                        "Find the derivative of x squared plus 2x minus 5"
                      </div>
                    </div>
                    
                    <div className="flex justify-center">
                      <ArrowRight className="w-5 h-5 text-muted-foreground animate-pulse" />
                    </div>
                    
                    <div className="text-left">
                      <div className="text-sm text-muted-foreground mb-1">Generated Formula:</div>
                      <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 rounded-lg p-3 text-center">
                        <div className="text-lg font-math">
                          d/dx(x² + 2x - 5) = 2x + 2
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Styles */}
      <style jsx>{`
        .bg-grid-pattern {
          background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
          background-size: 20px 20px;
        }
        
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
        }
        
        @keyframes float-delayed {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-15px); }
        }
        
        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
        
        .animate-float-delayed {
          animation: float-delayed 8s ease-in-out infinite;
        }
      `}</style>
    </section>
  )
}
