import { evaluate, parse, simplify } from 'mathjs'

export interface FormulaGenerationRequest {
  naturalLanguage: string
  context?: 'algebra' | 'calculus' | 'statistics' | 'geometry' | 'trigonometry'
  complexity?: 'basic' | 'intermediate' | 'advanced'
  variables?: string[]
  constraints?: string[]
}

export interface FormulaGenerationResult {
  formula: string
  latex: string
  explanation: string
  confidence: number
  alternatives: Array<{
    formula: string
    latex: string
    confidence: number
  }>
  steps?: string[]
  variables: Array<{
    name: string
    description: string
    type: 'independent' | 'dependent' | 'parameter'
  }>
}

export class FormulaGenerator {
  private patterns: Map<string, Array<{
    pattern: RegExp
    template: string
    context: string
    complexity: string
  }>>

  constructor() {
    this.patterns = new Map()
    this.initializePatterns()
  }

  private initializePatterns() {
    // Quadratic patterns
    this.patterns.set('quadratic', [
      {
        pattern: /quadratic.*function.*vertex.*\((-?\d+),\s*(-?\d+)\)/i,
        template: '(x - $1)^2 + $2',
        context: 'algebra',
        complexity: 'intermediate'
      },
      {
        pattern: /parabola.*opens?\s+(up|down).*vertex.*\((-?\d+),\s*(-?\d+)\)/i,
        template: '$1 === "up" ? "(x - $2)^2 + $3" : "-(x - $2)^2 + $3"',
        context: 'algebra',
        complexity: 'intermediate'
      },
      {
        pattern: /quadratic.*roots?.*(-?\d+).*and.*(-?\d+)/i,
        template: '(x - $1)(x - $2)',
        context: 'algebra',
        complexity: 'basic'
      }
    ])

    // Linear patterns
    this.patterns.set('linear', [
      {
        pattern: /line.*slope.*(-?\d+(?:\.\d+)?).*y-intercept.*(-?\d+(?:\.\d+)?)/i,
        template: '$1*x + $2',
        context: 'algebra',
        complexity: 'basic'
      },
      {
        pattern: /line.*through.*\((-?\d+),\s*(-?\d+)\).*and.*\((-?\d+),\s*(-?\d+)\)/i,
        template: 'calculateLineThroughPoints($1, $2, $3, $4)',
        context: 'algebra',
        complexity: 'intermediate'
      }
    ])

    // Trigonometric patterns
    this.patterns.set('trigonometric', [
      {
        pattern: /sine.*wave.*amplitude.*(-?\d+(?:\.\d+)?).*period.*(-?\d+(?:\.\d+)?)/i,
        template: '$1 * sin(2*pi*x / $2)',
        context: 'trigonometry',
        complexity: 'intermediate'
      },
      {
        pattern: /cosine.*function.*shifted.*(-?\d+(?:\.\d+)?).*units/i,
        template: 'cos(x + $1)',
        context: 'trigonometry',
        complexity: 'basic'
      }
    ])

    // Exponential patterns
    this.patterns.set('exponential', [
      {
        pattern: /exponential.*growth.*rate.*(-?\d+(?:\.\d+)?).*initial.*(-?\d+(?:\.\d+)?)/i,
        template: '$2 * exp($1 * x)',
        context: 'calculus',
        complexity: 'intermediate'
      },
      {
        pattern: /exponential.*decay.*half-life.*(-?\d+(?:\.\d+)?)/i,
        template: 'exp(-ln(2) * x / $1)',
        context: 'calculus',
        complexity: 'advanced'
      }
    ])

    // Calculus patterns
    this.patterns.set('calculus', [
      {
        pattern: /derivative.*of.*(.+)/i,
        template: 'derivative("$1", "x")',
        context: 'calculus',
        complexity: 'intermediate'
      },
      {
        pattern: /integral.*of.*(.+)/i,
        template: 'integral("$1", "x")',
        context: 'calculus',
        complexity: 'advanced'
      }
    ])
  }

  async generateFormula(request: FormulaGenerationRequest): Promise<FormulaGenerationResult> {
    const { naturalLanguage, context, complexity = 'intermediate' } = request

    // Try to match patterns
    const matches = this.findPatternMatches(naturalLanguage, context, complexity)
    
    if (matches.length === 0) {
      return this.generateFallbackFormula(request)
    }

    // Select best match
    const bestMatch = matches[0]
    const formula = this.processTemplate(bestMatch.template, bestMatch.groups)
    
    // Generate alternatives
    const alternatives = matches.slice(1, 4).map(match => ({
      formula: this.processTemplate(match.template, match.groups),
      latex: this.convertToLatex(this.processTemplate(match.template, match.groups)),
      confidence: match.confidence
    }))

    return {
      formula,
      latex: this.convertToLatex(formula),
      explanation: this.generateExplanation(formula, naturalLanguage),
      confidence: bestMatch.confidence,
      alternatives,
      steps: this.generateSteps(formula),
      variables: this.extractVariables(formula)
    }
  }

  private findPatternMatches(text: string, context?: string, complexity?: string) {
    const matches: Array<{
      template: string
      groups: string[]
      confidence: number
      context: string
      complexity: string
    }> = []

    for (const [category, patterns] of this.patterns.entries()) {
      for (const pattern of patterns) {
        // Filter by context and complexity if specified
        if (context && pattern.context !== context) continue
        if (complexity && pattern.complexity !== complexity) continue

        const match = text.match(pattern.pattern)
        if (match) {
          const confidence = this.calculateConfidence(match, pattern, text)
          matches.push({
            template: pattern.template,
            groups: match.slice(1),
            confidence,
            context: pattern.context,
            complexity: pattern.complexity
          })
        }
      }
    }

    return matches.sort((a, b) => b.confidence - a.confidence)
  }

  private calculateConfidence(match: RegExpMatchArray, pattern: any, text: string): number {
    let confidence = 0.7 // Base confidence

    // Boost confidence for exact keyword matches
    const keywords = ['function', 'equation', 'formula', 'graph', 'plot']
    const keywordMatches = keywords.filter(keyword => 
      text.toLowerCase().includes(keyword)
    ).length
    confidence += keywordMatches * 0.05

    // Boost confidence for numerical values
    const numberMatches = (text.match(/-?\d+(?:\.\d+)?/g) || []).length
    confidence += Math.min(numberMatches * 0.03, 0.15)

    // Reduce confidence for very short matches
    if (match[0].length < 10) confidence -= 0.1

    return Math.min(Math.max(confidence, 0.1), 0.95)
  }

  private processTemplate(template: string, groups: string[]): string {
    let result = template
    
    // Replace numbered placeholders
    groups.forEach((group, index) => {
      result = result.replace(new RegExp(`\\$${index + 1}`, 'g'), group)
    })

    // Handle special functions
    if (result.includes('calculateLineThroughPoints')) {
      result = this.calculateLineThroughPoints(result, groups)
    }

    return result
  }

  private calculateLineThroughPoints(template: string, groups: string[]): string {
    if (groups.length >= 4) {
      const [x1, y1, x2, y2] = groups.map(Number)
      const slope = (y2 - y1) / (x2 - x1)
      const intercept = y1 - slope * x1
      return `${slope}*x + ${intercept}`
    }
    return 'x'
  }

  private convertToLatex(formula: string): string {
    return formula
      .replace(/\*/g, ' \\cdot ')
      .replace(/\^/g, '^')
      .replace(/sqrt\(([^)]+)\)/g, '\\sqrt{$1}')
      .replace(/sin\(([^)]+)\)/g, '\\sin($1)')
      .replace(/cos\(([^)]+)\)/g, '\\cos($1)')
      .replace(/tan\(([^)]+)\)/g, '\\tan($1)')
      .replace(/exp\(([^)]+)\)/g, 'e^{$1}')
      .replace(/ln\(([^)]+)\)/g, '\\ln($1)')
      .replace(/log\(([^)]+)\)/g, '\\log($1)')
      .replace(/pi/g, '\\pi')
      .replace(/(\d+)\/(\d+)/g, '\\frac{$1}{$2}')
  }

  private generateExplanation(formula: string, naturalLanguage: string): string {
    const explanations = [
      `This formula represents the mathematical relationship described in your request: "${naturalLanguage}".`,
      `The generated formula ${formula} captures the key mathematical concepts from your description.`,
      `Based on your input, this formula models the mathematical behavior you described.`
    ]
    
    return explanations[Math.floor(Math.random() * explanations.length)]
  }

  private generateSteps(formula: string): string[] {
    const steps = [
      'Analyzed the natural language input for mathematical keywords and patterns',
      'Identified the mathematical context and complexity level',
      'Matched the description to known formula templates',
      `Generated the formula: ${formula}`,
      'Converted to LaTeX notation for proper mathematical display',
      'Validated the formula structure and syntax'
    ]
    
    return steps
  }

  private extractVariables(formula: string): Array<{
    name: string
    description: string
    type: 'independent' | 'dependent' | 'parameter'
  }> {
    const variables: Array<{
      name: string
      description: string
      type: 'independent' | 'dependent' | 'parameter'
    }> = []

    // Extract variable names (letters that aren't function names)
    const variableMatches = formula.match(/\b[a-zA-Z]\b/g) || []
    const uniqueVars = [...new Set(variableMatches)]
    
    uniqueVars.forEach(varName => {
      if (!['sin', 'cos', 'tan', 'exp', 'ln', 'log', 'pi', 'e'].includes(varName)) {
        variables.push({
          name: varName,
          description: varName === 'x' ? 'Independent variable' : 
                      varName === 'y' ? 'Dependent variable' : 
                      'Parameter',
          type: varName === 'x' ? 'independent' : 
                varName === 'y' ? 'dependent' : 
                'parameter'
        })
      }
    })

    return variables
  }

  private generateFallbackFormula(request: FormulaGenerationRequest): FormulaGenerationResult {
    // Simple fallback for when no patterns match
    const fallbackFormulas = [
      'x^2',
      'sin(x)',
      'exp(x)',
      'x + 1',
      'x^2 + x + 1'
    ]
    
    const formula = fallbackFormulas[Math.floor(Math.random() * fallbackFormulas.length)]
    
    return {
      formula,
      latex: this.convertToLatex(formula),
      explanation: 'Generated a basic mathematical formula. For better results, try describing your mathematical relationship more specifically.',
      confidence: 0.3,
      alternatives: [],
      variables: this.extractVariables(formula)
    }
  }
}
