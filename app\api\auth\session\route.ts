import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

interface User {
  id: string
  email: string
  name: string
  avatar?: string
  role: 'user' | 'premium' | 'admin'
  createdAt: string
  lastLoginAt: string
  preferences: {
    theme: 'light' | 'dark' | 'system'
    notifications: boolean
    defaultComplexity: 'basic' | 'intermediate' | 'advanced'
  }
}

interface Session {
  id: string
  userId: string
  expiresAt: string
  createdAt: string
  lastAccessedAt: string
}

// Mock data - in production, use proper database and session management
const mockUsers: Record<string, User> = {
  'user1': {
    id: 'user1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'user',
    createdAt: '2024-01-01T00:00:00Z',
    lastLoginAt: new Date().toISOString(),
    preferences: {
      theme: 'system',
      notifications: true,
      defaultComplexity: 'intermediate'
    }
  }
}

const mockSessions: Record<string, Session> = {}

function generateSessionId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

function isSessionValid(session: Session): boolean {
  return new Date(session.expiresAt) > new Date()
}

// GET /api/auth/session - Get current session
export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const sessionId = cookieStore.get('session-id')?.value

    if (!sessionId) {
      return NextResponse.json(
        { error: 'No session found' },
        { status: 401 }
      )
    }

    const session = mockSessions[sessionId]
    if (!session || !isSessionValid(session)) {
      return NextResponse.json(
        { error: 'Invalid or expired session' },
        { status: 401 }
      )
    }

    const user = mockUsers[session.userId]
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Update last accessed time
    session.lastAccessedAt = new Date().toISOString()

    return NextResponse.json({
      success: true,
      data: {
        user,
        session: {
          id: session.id,
          expiresAt: session.expiresAt,
          lastAccessedAt: session.lastAccessedAt
        }
      }
    })

  } catch (error) {
    console.error('Session check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/auth/session - Create new session (login)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, password } = body

    // Basic validation
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Mock authentication - in production, use proper password hashing
    const user = Object.values(mockUsers).find(u => u.email === email)
    if (!user || password !== 'password123') { // Mock password
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Create session
    const sessionId = generateSessionId()
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    
    const session: Session = {
      id: sessionId,
      userId: user.id,
      expiresAt: expiresAt.toISOString(),
      createdAt: new Date().toISOString(),
      lastAccessedAt: new Date().toISOString()
    }

    mockSessions[sessionId] = session

    // Update user last login
    user.lastLoginAt = new Date().toISOString()

    // Set cookie
    const response = NextResponse.json({
      success: true,
      data: {
        user,
        session: {
          id: session.id,
          expiresAt: session.expiresAt
        }
      }
    })

    response.cookies.set('session-id', sessionId, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      expires: expiresAt
    })

    return response

  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/auth/session - Delete session (logout)
export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const sessionId = cookieStore.get('session-id')?.value

    if (sessionId && mockSessions[sessionId]) {
      delete mockSessions[sessionId]
    }

    const response = NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    })

    // Clear cookie
    response.cookies.delete('session-id')

    return response

  } catch (error) {
    console.error('Logout error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/auth/session - Update session/user preferences
export async function PUT(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const sessionId = cookieStore.get('session-id')?.value

    if (!sessionId) {
      return NextResponse.json(
        { error: 'No session found' },
        { status: 401 }
      )
    }

    const session = mockSessions[sessionId]
    if (!session || !isSessionValid(session)) {
      return NextResponse.json(
        { error: 'Invalid or expired session' },
        { status: 401 }
      )
    }

    const user = mockUsers[session.userId]
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    const body = await request.json()
    const { preferences, name } = body

    // Update user data
    if (name && typeof name === 'string' && name.trim().length > 0) {
      user.name = name.trim()
    }

    if (preferences && typeof preferences === 'object') {
      if (preferences.theme && ['light', 'dark', 'system'].includes(preferences.theme)) {
        user.preferences.theme = preferences.theme
      }
      if (typeof preferences.notifications === 'boolean') {
        user.preferences.notifications = preferences.notifications
      }
      if (preferences.defaultComplexity && ['basic', 'intermediate', 'advanced'].includes(preferences.defaultComplexity)) {
        user.preferences.defaultComplexity = preferences.defaultComplexity
      }
    }

    return NextResponse.json({
      success: true,
      data: { user }
    })

  } catch (error) {
    console.error('Session update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
