# FormulaForge API Documentation

Welcome to the FormulaForge API documentation. This API provides powerful mathematical computation, natural language processing, and collaboration features.

## Base URL

```
https://formulaforge.com/api
```

For local development:
```
http://localhost:3000/api
```

## Authentication

Most endpoints require authentication using Bearer tokens. Include your token in the Authorization header:

```
Authorization: Bearer YOUR_TOKEN_HERE
```

## Rate Limiting

API endpoints are rate-limited to ensure fair usage:

- **Mathematical Operations**: 100 requests per minute
- **NLP Processing**: 50 requests per minute
- **Code Export**: 100 requests per minute
- **Collaboration**: 20 room creations per minute
- **Formula Library**: 50 saves per minute

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## Endpoints Overview

### Mathematical Operations
- `POST /api/math/evaluate` - Evaluate mathematical expressions
- `GET /api/math/evaluate` - Health check

### Natural Language Processing
- `POST /api/nlp/process` - Convert natural language to formulas
- `GET /api/nlp/process` - Service capabilities

### Code Export
- `POST /api/export/code` - Export formulas to programming languages

### Formula Library
- `GET /api/formulas/library` - Browse formula library
- `POST /api/formulas/library` - Save formula to library
- `PUT /api/formulas/library` - Update formula
- `DELETE /api/formulas/library` - Delete formula

### Collaboration
- `GET /api/collaboration/rooms` - List collaboration rooms
- `POST /api/collaboration/rooms` - Create new room
- `DELETE /api/collaboration/rooms` - Delete room

## Mathematical Operations API

### Evaluate Expression

Evaluate mathematical expressions with support for variables, functions, and complex operations.

**Endpoint:** `POST /api/math/evaluate`

**Request Body:**
```json
{
  "expression": "2*x + 5",
  "variables": {"x": 3},
  "operation": "evaluate"
}
```

**Response:**
```json
{
  "success": true,
  "operation": "evaluate",
  "input": "2*x + 5",
  "result": {
    "result": 11,
    "type": "number",
    "simplified": "2*x + 5"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Solve Equations

Solve mathematical equations step-by-step.

**Request Body:**
```json
{
  "expression": "2*x + 5 = 15",
  "operation": "solve",
  "variable": "x"
}
```

**Response:**
```json
{
  "success": true,
  "result": {
    "variable": "x",
    "solutions": [5],
    "steps": [
      "Original equation: 2*x + 5 = 15",
      "Subtract 5 from both sides: 2*x = 10",
      "Divide by 2: x = 5"
    ],
    "method": "linear"
  }
}
```

### Calculate Derivatives

Find derivatives of mathematical functions.

**Request Body:**
```json
{
  "expression": "x^3 + 2*x^2 - 5*x + 1",
  "operation": "derivative",
  "variable": "x"
}
```

**Response:**
```json
{
  "success": true,
  "result": {
    "original": "x^3 + 2*x^2 - 5*x + 1",
    "derivative": "3*x^2 + 4*x - 5",
    "latex": "3x^2 + 4x - 5",
    "steps": [
      "Finding derivative with respect to x",
      "Using power rule: d/dx[x^n] = n*x^(n-1)",
      "d/dx[x^3] = 3*x^2",
      "d/dx[2*x^2] = 4*x",
      "d/dx[-5*x] = -5",
      "d/dx[1] = 0"
    ]
  }
}
```

### Matrix Operations

Perform matrix calculations.

**Request Body:**
```json
{
  "operation": "matrix",
  "matrices": [
    [[1, 2], [3, 4]],
    [[5, 6], [7, 8]]
  ],
  "matrixOperation": "multiply"
}
```

**Response:**
```json
{
  "success": true,
  "result": {
    "result": [[19, 22], [43, 50]],
    "type": "matrix"
  }
}
```

## Natural Language Processing API

### Process Natural Language

Convert natural language descriptions into mathematical formulas.

**Endpoint:** `POST /api/nlp/process`

**Request Body:**
```json
{
  "input": "find the derivative of x squared plus 2x",
  "useAI": true,
  "context": {}
}
```

**Response:**
```json
{
  "success": true,
  "input": "find the derivative of x squared plus 2x",
  "nlp": {
    "intent": "derivative",
    "entities": {
      "functions": ["derivative"],
      "operations": ["+"]
    },
    "formula": "x^2 + 2*x",
    "confidence": 0.95,
    "explanation": "Taking derivative of x² + 2x",
    "variables": ["x"],
    "operation": "derivative"
  },
  "evaluation": {
    "original": "x^2 + 2*x",
    "derivative": "2*x + 2",
    "steps": ["Using power rule", "d/dx[x^2] = 2*x", "d/dx[2*x] = 2"]
  },
  "suggestions": []
}
```

## Code Export API

### Export to Programming Languages

Export mathematical formulas as executable code in various programming languages.

**Endpoint:** `POST /api/export/code`

**Request Body:**
```json
{
  "formula": "x^2 + 2*x + 1",
  "language": "python",
  "variables": {"x": 1},
  "functionName": "quadratic",
  "includeComments": true,
  "includeTests": true
}
```

**Response:**
```json
{
  "success": true,
  "code": "def quadratic(x):\n    \"\"\"\n    Calculate: x^2 + 2*x + 1\n    \"\"\"\n    return x**2 + 2*x + 1\n\nif __name__ == \"__main__\":\n    result = quadratic(1)\n    print(f\"Result: {result}\")",
  "filename": "quadratic.py",
  "language": "python",
  "metadata": {
    "formula": "x^2 + 2*x + 1",
    "variables": {"x": 1},
    "functionName": "quadratic",
    "generatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**Supported Languages:**
- Python (`python`)
- JavaScript (`javascript`)
- TypeScript (`typescript`)
- MATLAB (`matlab`)
- R (`r`)
- Julia (`julia`)
- C++ (`cpp`)
- Java (`java`)
- Go (`go`)
- Rust (`rust`)

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error description",
  "details": "Additional error details (development only)"
}
```

**Common HTTP Status Codes:**
- `200` - Success
- `400` - Bad Request (invalid input)
- `401` - Unauthorized (missing/invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## SDKs and Libraries

### JavaScript/TypeScript
```bash
npm install @formulaforge/sdk
```

```javascript
import { FormulaForge } from '@formulaforge/sdk'

const client = new FormulaForge('your-api-key')

const result = await client.evaluate('2*x + 5', { x: 3 })
console.log(result) // 11
```

### Python
```bash
pip install formulaforge
```

```python
from formulaforge import FormulaForge

client = FormulaForge('your-api-key')
result = client.evaluate('2*x + 5', {'x': 3})
print(result)  # 11
```

## Webhooks

FormulaForge supports webhooks for real-time notifications:

- Formula computation completed
- Collaboration room activity
- Library formula updates

Configure webhooks in your dashboard at `/dashboard/webhooks`.

## Support

- **Documentation**: [docs.formulaforge.com](https://docs.formulaforge.com)
- **API Status**: [status.formulaforge.com](https://status.formulaforge.com)
- **Support Email**: [<EMAIL>](mailto:<EMAIL>)
- **GitHub Issues**: [github.com/HectorTa1989/FormulaForge/issues](https://github.com/HectorTa1989/FormulaForge/issues)
