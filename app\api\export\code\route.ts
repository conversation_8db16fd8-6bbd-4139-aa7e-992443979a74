import { NextRequest, NextResponse } from 'next/server'
import { rateLimit } from '@/lib/rate-limit'

// Rate limiting
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500,
})

interface CodeExportRequest {
  formula: string
  language: string
  variables?: Record<string, any>
  functionName?: string
  includeComments?: boolean
  includeTests?: boolean
}

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 100, 'CACHE_TOKEN') // 100 exports per minute
    } catch {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const body: CodeExportRequest = await request.json()
    const { 
      formula, 
      language, 
      variables = {}, 
      functionName = 'calculate',
      includeComments = true,
      includeTests = false
    } = body

    // Validate input
    if (!formula || !language) {
      return NextResponse.json(
        { error: 'Formula and language are required' },
        { status: 400 }
      )
    }

    const supportedLanguages = ['python', 'javascript', 'typescript', 'matlab', 'r', 'julia', 'cpp', 'java', 'go', 'rust']
    if (!supportedLanguages.includes(language.toLowerCase())) {
      return NextResponse.json(
        { error: `Unsupported language. Supported: ${supportedLanguages.join(', ')}` },
        { status: 400 }
      )
    }

    // Generate code based on language
    const generatedCode = generateCode(formula, language.toLowerCase(), {
      variables,
      functionName,
      includeComments,
      includeTests,
    })

    return NextResponse.json({
      success: true,
      code: generatedCode.code,
      filename: generatedCode.filename,
      language: language.toLowerCase(),
      metadata: {
        formula,
        variables,
        functionName,
        generatedAt: new Date().toISOString(),
      },
    })

  } catch (error) {
    console.error('Code export error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateCode(
  formula: string, 
  language: string, 
  options: {
    variables: Record<string, any>
    functionName: string
    includeComments: boolean
    includeTests: boolean
  }
): { code: string; filename: string } {
  const { variables, functionName, includeComments, includeTests } = options

  switch (language) {
    case 'python':
      return generatePythonCode(formula, variables, functionName, includeComments, includeTests)
    
    case 'javascript':
      return generateJavaScriptCode(formula, variables, functionName, includeComments, includeTests)
    
    case 'typescript':
      return generateTypeScriptCode(formula, variables, functionName, includeComments, includeTests)
    
    case 'matlab':
      return generateMatlabCode(formula, variables, functionName, includeComments, includeTests)
    
    case 'r':
      return generateRCode(formula, variables, functionName, includeComments, includeTests)
    
    case 'julia':
      return generateJuliaCode(formula, variables, functionName, includeComments, includeTests)
    
    case 'cpp':
      return generateCppCode(formula, variables, functionName, includeComments, includeTests)
    
    case 'java':
      return generateJavaCode(formula, variables, functionName, includeComments, includeTests)
    
    case 'go':
      return generateGoCode(formula, variables, functionName, includeComments, includeTests)
    
    case 'rust':
      return generateRustCode(formula, variables, functionName, includeComments, includeTests)
    
    default:
      throw new Error(`Unsupported language: ${language}`)
  }
}

function generatePythonCode(
  formula: string, 
  variables: Record<string, any>, 
  functionName: string, 
  includeComments: boolean, 
  includeTests: boolean
): { code: string; filename: string } {
  const convertedFormula = convertFormulaToPython(formula)
  const varList = Object.keys(variables)
  
  let code = ''
  
  if (includeComments) {
    code += `"""
Generated mathematical function from FormulaForge
Original formula: ${formula}
Generated on: ${new Date().toISOString()}
"""

import math
import numpy as np

`
  } else {
    code += 'import math\nimport numpy as np\n\n'
  }

  code += `def ${functionName}(${varList.join(', ')}):`
  
  if (includeComments) {
    code += `
    """
    Calculate: ${formula}
    
    Parameters:
    ${varList.map(v => `    ${v}: float or array-like`).join('\n')}
    
    Returns:
    float or array: Result of the calculation
    """
`
  }
  
  code += `
    return ${convertedFormula}

`

  if (includeTests) {
    code += `
# Example usage and tests
if __name__ == "__main__":
    # Test with sample values
    ${varList.map(v => `${v} = ${variables[v] || 1}`).join('\n    ')}
    
    result = ${functionName}(${varList.join(', ')})
    print(f"Result: {result}")
    
    # Test with arrays (if applicable)
    try:
        ${varList.map(v => `${v}_array = np.array([${variables[v] || 1}, ${(variables[v] || 1) * 2}, ${(variables[v] || 1) * 3}])`).join('\n        ')}
        result_array = ${functionName}(${varList.map(v => `${v}_array`).join(', ')})
        print(f"Array result: {result_array}")
    except Exception as e:
        print(f"Array calculation not supported: {e}")
`
  }

  return { code, filename: `${functionName}.py` }
}

function generateJavaScriptCode(
  formula: string, 
  variables: Record<string, any>, 
  functionName: string, 
  includeComments: boolean, 
  includeTests: boolean
): { code: string; filename: string } {
  const convertedFormula = convertFormulaToJavaScript(formula)
  const varList = Object.keys(variables)
  
  let code = ''
  
  if (includeComments) {
    code += `/**
 * Generated mathematical function from FormulaForge
 * Original formula: ${formula}
 * Generated on: ${new Date().toISOString()}
 */

`
  }

  code += `function ${functionName}(${varList.join(', ')}) {`
  
  if (includeComments) {
    code += `
    /**
     * Calculate: ${formula}
     * @param {number} ${varList.join(' - Input parameter\n     * @param {number} ')} - Input parameter
     * @returns {number} Result of the calculation
     */
`
  }
  
  code += `
    return ${convertedFormula};
}

`

  if (includeTests) {
    code += `
// Example usage and tests
console.log('Testing ${functionName}...');

// Test with sample values
${varList.map(v => `const ${v} = ${variables[v] || 1};`).join('\n')}

const result = ${functionName}(${varList.join(', ')});
console.log(\`Result: \${result}\`);

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ${functionName};
}
`
  }

  return { code, filename: `${functionName}.js` }
}

function generateTypeScriptCode(
  formula: string, 
  variables: Record<string, any>, 
  functionName: string, 
  includeComments: boolean, 
  includeTests: boolean
): { code: string; filename: string } {
  const convertedFormula = convertFormulaToJavaScript(formula)
  const varList = Object.keys(variables)
  
  let code = ''
  
  if (includeComments) {
    code += `/**
 * Generated mathematical function from FormulaForge
 * Original formula: ${formula}
 * Generated on: ${new Date().toISOString()}
 */

`
  }

  code += `export function ${functionName}(${varList.map(v => `${v}: number`).join(', ')}): number {`
  
  if (includeComments) {
    code += `
    /**
     * Calculate: ${formula}
     */
`
  }
  
  code += `
    return ${convertedFormula};
}

`

  if (includeTests) {
    code += `
// Example usage and tests
console.log('Testing ${functionName}...');

// Test with sample values
${varList.map(v => `const ${v}: number = ${variables[v] || 1};`).join('\n')}

const result: number = ${functionName}(${varList.join(', ')});
console.log(\`Result: \${result}\`);
`
  }

  return { code, filename: `${functionName}.ts` }
}

function generateMatlabCode(
  formula: string, 
  variables: Record<string, any>, 
  functionName: string, 
  includeComments: boolean, 
  includeTests: boolean
): { code: string; filename: string } {
  const convertedFormula = convertFormulaToMatlab(formula)
  const varList = Object.keys(variables)
  
  let code = ''
  
  if (includeComments) {
    code += `% Generated mathematical function from FormulaForge
% Original formula: ${formula}
% Generated on: ${new Date().toISOString()}

`
  }

  code += `function result = ${functionName}(${varList.join(', ')})`
  
  if (includeComments) {
    code += `
    % Calculate: ${formula}
    % Inputs: ${varList.join(', ')}
    % Output: result
`
  }
  
  code += `
    result = ${convertedFormula};
end

`

  if (includeTests) {
    code += `
% Example usage and tests
fprintf('Testing ${functionName}...\\n');

% Test with sample values
${varList.map(v => `${v} = ${variables[v] || 1};`).join('\n')}

result = ${functionName}(${varList.join(', ')});
fprintf('Result: %f\\n', result);
`
  }

  return { code, filename: `${functionName}.m` }
}

function generateRCode(
  formula: string, 
  variables: Record<string, any>, 
  functionName: string, 
  includeComments: boolean, 
  includeTests: boolean
): { code: string; filename: string } {
  const convertedFormula = convertFormulaToR(formula)
  const varList = Object.keys(variables)
  
  let code = ''
  
  if (includeComments) {
    code += `# Generated mathematical function from FormulaForge
# Original formula: ${formula}
# Generated on: ${new Date().toISOString()}

`
  }

  code += `${functionName} <- function(${varList.join(', ')}) {`
  
  if (includeComments) {
    code += `
  # Calculate: ${formula}
  # Parameters: ${varList.join(', ')}
  # Returns: numeric result
`
  }
  
  code += `
  return(${convertedFormula})
}

`

  if (includeTests) {
    code += `
# Example usage and tests
cat("Testing ${functionName}...\\n")

# Test with sample values
${varList.map(v => `${v} <- ${variables[v] || 1}`).join('\n')}

result <- ${functionName}(${varList.join(', ')})
cat("Result:", result, "\\n")
`
  }

  return { code, filename: `${functionName}.R` }
}

// Additional language generators would follow similar patterns...
function generateJuliaCode(formula: string, variables: Record<string, any>, functionName: string, includeComments: boolean, includeTests: boolean): { code: string; filename: string } {
  // Julia implementation
  return { code: `# Julia implementation for ${functionName}\nfunction ${functionName}(${Object.keys(variables).join(', ')})\n    return ${formula}\nend`, filename: `${functionName}.jl` }
}

function generateCppCode(formula: string, variables: Record<string, any>, functionName: string, includeComments: boolean, includeTests: boolean): { code: string; filename: string } {
  // C++ implementation
  return { code: `// C++ implementation\n#include <cmath>\ndouble ${functionName}(${Object.keys(variables).map(v => `double ${v}`).join(', ')}) {\n    return ${formula};\n}`, filename: `${functionName}.cpp` }
}

function generateJavaCode(formula: string, variables: Record<string, any>, functionName: string, includeComments: boolean, includeTests: boolean): { code: string; filename: string } {
  // Java implementation
  return { code: `// Java implementation\npublic static double ${functionName}(${Object.keys(variables).map(v => `double ${v}`).join(', ')}) {\n    return ${formula};\n}`, filename: `${functionName}.java` }
}

function generateGoCode(formula: string, variables: Record<string, any>, functionName: string, includeComments: boolean, includeTests: boolean): { code: string; filename: string } {
  // Go implementation
  return { code: `// Go implementation\nfunc ${functionName}(${Object.keys(variables).map(v => `${v} float64`).join(', ')}) float64 {\n    return ${formula}\n}`, filename: `${functionName}.go` }
}

function generateRustCode(formula: string, variables: Record<string, any>, functionName: string, includeComments: boolean, includeTests: boolean): { code: string; filename: string } {
  // Rust implementation
  return { code: `// Rust implementation\nfn ${functionName}(${Object.keys(variables).map(v => `${v}: f64`).join(', ')}) -> f64 {\n    ${formula}\n}`, filename: `${functionName}.rs` }
}

// Formula conversion functions
function convertFormulaToPython(formula: string): string {
  return formula
    .replace(/\^/g, '**')
    .replace(/sin/g, 'math.sin')
    .replace(/cos/g, 'math.cos')
    .replace(/tan/g, 'math.tan')
    .replace(/log/g, 'math.log')
    .replace(/sqrt/g, 'math.sqrt')
    .replace(/pi/g, 'math.pi')
    .replace(/e/g, 'math.e')
}

function convertFormulaToJavaScript(formula: string): string {
  return formula
    .replace(/\^/g, '**')
    .replace(/sin/g, 'Math.sin')
    .replace(/cos/g, 'Math.cos')
    .replace(/tan/g, 'Math.tan')
    .replace(/log/g, 'Math.log')
    .replace(/sqrt/g, 'Math.sqrt')
    .replace(/pi/g, 'Math.PI')
    .replace(/e/g, 'Math.E')
}

function convertFormulaToMatlab(formula: string): string {
  return formula
    .replace(/\*\*/g, '^')
    .replace(/Math\./g, '')
    .replace(/pi/g, 'pi')
    .replace(/e/g, 'exp(1)')
}

function convertFormulaToR(formula: string): string {
  return formula
    .replace(/\^/g, '^')
    .replace(/Math\./g, '')
    .replace(/pi/g, 'pi')
    .replace(/e/g, 'exp(1)')
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
