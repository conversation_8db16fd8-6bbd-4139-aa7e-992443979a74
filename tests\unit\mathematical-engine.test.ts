import { MathematicalEngine } from '@/backend/engines/mathematical-engine'

describe('MathematicalEngine', () => {
  let engine: MathematicalEngine

  beforeEach(() => {
    engine = new MathematicalEngine()
  })

  describe('evaluate', () => {
    it('should evaluate simple arithmetic expressions', () => {
      const result = engine.evaluate('2 + 3')
      expect(result.result).toBe(5)
      expect(result.type).toBe('number')
      expect(result.error).toBeUndefined()
    })

    it('should evaluate expressions with variables', () => {
      const result = engine.evaluate('x + y', { x: 5, y: 3 })
      expect(result.result).toBe(8)
      expect(result.type).toBe('number')
    })

    it('should handle mathematical functions', () => {
      const result = engine.evaluate('sin(pi/2)')
      expect(result.result).toBeCloseTo(1, 10)
      expect(result.type).toBe('number')
    })

    it('should handle complex expressions', () => {
      const result = engine.evaluate('sqrt(x^2 + y^2)', { x: 3, y: 4 })
      expect(result.result).toBe(5)
      expect(result.type).toBe('number')
    })

    it('should return error for invalid expressions', () => {
      const result = engine.evaluate('invalid expression')
      expect(result.type).toBe('error')
      expect(result.error).toBeDefined()
    })

    it('should simplify expressions when possible', () => {
      const result = engine.evaluate('x + x', { x: 5 })
      expect(result.result).toBe(10)
      expect(result.simplified).toBeDefined()
    })
  })

  describe('solveEquation', () => {
    it('should solve linear equations', () => {
      const result = engine.solveEquation('2*x + 5 = 15', 'x')
      expect(result.variable).toBe('x')
      expect(result.solutions).toContain(5)
      expect(result.method).toBe('linear')
      expect(result.steps.length).toBeGreaterThan(0)
    })

    it('should handle equations with no solution', () => {
      const result = engine.solveEquation('x + 1 = x + 2', 'x')
      expect(result.solutions).toHaveLength(0)
      expect(result.steps).toContain(expect.stringMatching(/no solution|contradiction/i))
    })

    it('should provide step-by-step solutions', () => {
      const result = engine.solveEquation('3*x - 7 = 8', 'x')
      expect(result.steps).toContain(expect.stringMatching(/original equation/i))
      expect(result.steps.length).toBeGreaterThan(2)
    })
  })

  describe('calculateDerivative', () => {
    it('should calculate derivatives of polynomials', () => {
      const result = engine.calculateDerivative('x^2', 'x')
      expect(result.derivative).toBe('2 * x')
      expect(result.latex).toBeDefined()
      expect(result.steps.length).toBeGreaterThan(0)
    })

    it('should handle trigonometric functions', () => {
      const result = engine.calculateDerivative('sin(x)', 'x')
      expect(result.derivative).toBe('cos(x)')
      expect(result.steps).toContain(expect.stringMatching(/trigonometric/i))
    })

    it('should handle complex expressions', () => {
      const result = engine.calculateDerivative('x^3 + 2*x^2 - 5*x + 1', 'x')
      expect(result.derivative).toBe('3 * x ^ 2 + 4 * x - 5')
      expect(result.steps).toContain(expect.stringMatching(/power rule/i))
    })

    it('should provide explanatory steps', () => {
      const result = engine.calculateDerivative('x^3', 'x')
      expect(result.steps).toContain(expect.stringMatching(/power rule/i))
      expect(result.steps.length).toBeGreaterThan(0)
    })
  })

  describe('calculateIntegral', () => {
    it('should integrate simple polynomials', () => {
      const result = engine.calculateIntegral('x', 'x')
      expect(result.integral).toBe('x^2/2')
      expect(result.steps.length).toBeGreaterThan(0)
    })

    it('should handle definite integrals', () => {
      const result = engine.calculateIntegral('x', 'x', [0, 2])
      expect(result.definite).toBe(2) // ∫₀² x dx = [x²/2]₀² = 2
      expect(result.steps).toContain(expect.stringMatching(/definite integral/i))
    })

    it('should provide step-by-step integration', () => {
      const result = engine.calculateIntegral('1', 'x')
      expect(result.integral).toBe('x')
      expect(result.steps).toContain(expect.stringMatching(/∫1 dx = x \+ C/i))
    })
  })

  describe('matrixOperations', () => {
    it('should add matrices', () => {
      const matrix1 = [[1, 2], [3, 4]]
      const matrix2 = [[5, 6], [7, 8]]
      const result = engine.matrixOperations('add', [matrix1, matrix2])
      
      expect(result.type).toBe('matrix')
      expect(result.result).toEqual([[6, 8], [10, 12]])
    })

    it('should multiply matrices', () => {
      const matrix1 = [[1, 2], [3, 4]]
      const matrix2 = [[5, 6], [7, 8]]
      const result = engine.matrixOperations('multiply', [matrix1, matrix2])
      
      expect(result.type).toBe('matrix')
      expect(result.result).toEqual([[19, 22], [43, 50]])
    })

    it('should calculate determinant', () => {
      const matrix = [[1, 2], [3, 4]]
      const result = engine.matrixOperations('determinant', [matrix])
      
      expect(result.type).toBe('number')
      expect(result.result).toBe(-2)
    })

    it('should transpose matrices', () => {
      const matrix = [[1, 2, 3], [4, 5, 6]]
      const result = engine.matrixOperations('transpose', [matrix])
      
      expect(result.type).toBe('matrix')
      expect(result.result).toEqual([[1, 4], [2, 5], [3, 6]])
    })

    it('should handle invalid operations', () => {
      const matrix = [[1, 2], [3, 4]]
      const result = engine.matrixOperations('invalid', [matrix])
      
      expect(result.type).toBe('error')
      expect(result.error).toContain('Unknown matrix operation')
    })
  })

  describe('statisticalAnalysis', () => {
    const testData = [1, 2, 3, 4, 5]

    it('should calculate mean', () => {
      const result = engine.statisticalAnalysis(testData, 'mean')
      expect(result.result).toBe(3)
      expect(result.type).toBe('number')
    })

    it('should calculate median', () => {
      const result = engine.statisticalAnalysis(testData, 'median')
      expect(result.result).toBe(3)
      expect(result.type).toBe('number')
    })

    it('should calculate standard deviation', () => {
      const result = engine.statisticalAnalysis(testData, 'standarddeviation')
      expect(result.result).toBeCloseTo(1.58, 2)
      expect(result.type).toBe('number')
    })

    it('should calculate variance', () => {
      const result = engine.statisticalAnalysis(testData, 'variance')
      expect(result.result).toBe(2.5)
      expect(result.type).toBe('number')
    })

    it('should handle invalid operations', () => {
      const result = engine.statisticalAnalysis(testData, 'invalid')
      expect(result.type).toBe('error')
      expect(result.error).toContain('Unknown statistical operation')
    })
  })

  describe('edge cases', () => {
    it('should handle division by zero', () => {
      const result = engine.evaluate('1/0')
      expect(result.result).toBe(Infinity)
      expect(result.type).toBe('number')
    })

    it('should handle undefined results', () => {
      const result = engine.evaluate('sqrt(-1)')
      expect(isNaN(result.result as number)).toBe(true)
      expect(result.type).toBe('number')
    })

    it('should handle empty expressions', () => {
      const result = engine.evaluate('')
      expect(result.type).toBe('error')
      expect(result.error).toBeDefined()
    })

    it('should handle very large numbers', () => {
      const result = engine.evaluate('10^100')
      expect(result.result).toBe(1e100)
      expect(result.type).toBe('number')
    })
  })

  describe('precision and accuracy', () => {
    it('should maintain precision for decimal calculations', () => {
      const result = engine.evaluate('0.1 + 0.2')
      expect(result.result).toBeCloseTo(0.3, 10)
    })

    it('should handle scientific notation', () => {
      const result = engine.evaluate('1.23e-4')
      expect(result.result).toBe(0.000123)
    })

    it('should work with custom precision settings', () => {
      const customEngine = new MathematicalEngine(6)
      const result = customEngine.evaluate('pi')
      expect(result.result.toString()).toMatch(/3\.14159/)
    })
  })
})
