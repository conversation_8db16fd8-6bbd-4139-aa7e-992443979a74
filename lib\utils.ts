import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Utility function to merge Tailwind CSS classes
 * Combines clsx for conditional classes and tailwind-merge for deduplication
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format numbers for display in mathematical contexts
 */
export function formatNumber(
  num: number,
  options: {
    precision?: number
    notation?: 'standard' | 'scientific' | 'engineering'
    compact?: boolean
  } = {}
): string {
  const { precision = 6, notation = 'standard', compact = false } = options

  if (!isFinite(num)) {
    if (isNaN(num)) return 'NaN'
    return num > 0 ? '∞' : '-∞'
  }

  if (compact && Math.abs(num) >= 1000) {
    return new Intl.NumberFormat('en-US', {
      notation: 'compact',
      maximumFractionDigits: 2,
    }).format(num)
  }

  switch (notation) {
    case 'scientific':
      return num.toExponential(precision)
    case 'engineering':
      const exp = Math.floor(Math.log10(Math.abs(num)) / 3) * 3
      const mantissa = num / Math.pow(10, exp)
      return `${mantissa.toFixed(precision)}e${exp}`
    default:
      return parseFloat(num.toPrecision(precision)).toString()
  }
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Throttle function for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * Generate a unique ID for mathematical expressions
 */
export function generateId(prefix: string = 'id'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}-${Date.now().toString(36)}`
}

/**
 * Validate mathematical expression syntax
 */
export function isValidMathExpression(expression: string): boolean {
  try {
    // Basic validation - check for balanced parentheses
    let balance = 0
    for (const char of expression) {
      if (char === '(') balance++
      if (char === ')') balance--
      if (balance < 0) return false
    }
    
    if (balance !== 0) return false
    
    // Check for invalid characters (basic validation)
    const validChars = /^[0-9+\-*/().\s\w^√∫∑∏πe,=<>!|&]+$/
    if (!validChars.test(expression)) return false
    
    return true
  } catch {
    return false
  }
}

/**
 * Escape special characters for LaTeX rendering
 */
export function escapeLatex(text: string): string {
  return text
    .replace(/\\/g, '\\\\')
    .replace(/\{/g, '\\{')
    .replace(/\}/g, '\\}')
    .replace(/\$/g, '\\$')
    .replace(/&/g, '\\&')
    .replace(/%/g, '\\%')
    .replace(/#/g, '\\#')
    .replace(/\^/g, '\\^{}')
    .replace(/_/g, '\\_')
    .replace(/~/g, '\\~{}')
}

/**
 * Convert mathematical expression to LaTeX format
 */
export function toLatex(expression: string): string {
  return expression
    // Replace common mathematical symbols
    .replace(/\*/g, ' \\cdot ')
    .replace(/sqrt\(([^)]+)\)/g, '\\sqrt{$1}')
    .replace(/\^([0-9]+)/g, '^{$1}')
    .replace(/\^([a-zA-Z]+)/g, '^{$1}')
    .replace(/([0-9]+)\/([0-9]+)/g, '\\frac{$1}{$2}')
    .replace(/pi/g, '\\pi')
    .replace(/infinity/g, '\\infty')
    .replace(/alpha/g, '\\alpha')
    .replace(/beta/g, '\\beta')
    .replace(/gamma/g, '\\gamma')
    .replace(/delta/g, '\\delta')
    .replace(/theta/g, '\\theta')
    .replace(/lambda/g, '\\lambda')
    .replace(/mu/g, '\\mu')
    .replace(/sigma/g, '\\sigma')
    .replace(/phi/g, '\\phi')
    .replace(/omega/g, '\\omega')
}

/**
 * Copy text to clipboard with fallback
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      const success = document.execCommand('copy')
      document.body.removeChild(textArea)
      return success
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    return false
  }
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Get relative time string
 */
export function getRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
  
  return date.toLocaleDateString()
}

/**
 * Validate email address
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Generate color from string (for user avatars, etc.)
 */
export function stringToColor(str: string): string {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  const hue = hash % 360
  return `hsl(${hue}, 70%, 50%)`
}

/**
 * Sleep utility for async operations
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Check if code is running on client side
 */
export function isClient(): boolean {
  return typeof window !== 'undefined'
}

/**
 * Safe JSON parse with fallback
 */
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json)
  } catch {
    return fallback
  }
}
