import { NextRequest, NextResponse } from 'next/server'

interface CollaborationSession {
  id: string
  name: string
  description: string
  createdAt: string
  lastModified: string
  ownerId: string
  participants: Array<{
    id: string
    name: string
    email: string
    role: 'owner' | 'editor' | 'viewer'
    joinedAt: string
    isActive: boolean
  }>
  isPublic: boolean
  shareLink: string
  documentCount: number
  settings: {
    allowAnonymous: boolean
    maxParticipants: number
    permissions: {
      canEdit: boolean
      canComment: boolean
      canShare: boolean
    }
  }
}

// In-memory storage (in production, use a proper database)
let sessions: CollaborationSession[] = [
  {
    id: '1',
    name: 'Calculus Problem Set',
    description: 'Working on integration and differentiation problems',
    createdAt: new Date('2024-01-15').toISOString(),
    lastModified: new Date('2024-01-20').toISOString(),
    ownerId: 'user1',
    participants: [
      {
        id: 'user1',
        name: '<PERSON>',
        email: '<EMAIL>',
        role: 'owner',
        joinedAt: new Date('2024-01-15').toISOString(),
        isActive: true
      },
      {
        id: 'user2',
        name: '<PERSON>',
        email: '<EMAIL>',
        role: 'editor',
        joinedAt: new Date('2024-01-16').toISOString(),
        isActive: false
      }
    ],
    isPublic: false,
    shareLink: 'https://formulaforge.com/session/1',
    documentCount: 5,
    settings: {
      allowAnonymous: false,
      maxParticipants: 10,
      permissions: {
        canEdit: true,
        canComment: true,
        canShare: true
      }
    }
  }
]

// Rate limiting
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

function getRateLimitKey(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : 'unknown'
  return ip
}

function checkRateLimit(key: string, limit: number = 30, windowMs: number = 60000): boolean {
  const now = Date.now()
  const record = rateLimitMap.get(key)
  
  if (!record || now > record.resetTime) {
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (record.count >= limit) {
    return false
  }
  
  record.count++
  return true
}

// GET /api/collaboration/sessions - List sessions
export async function GET(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitKey = getRateLimitKey(request)
    if (!checkRateLimit(rateLimitKey)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId') || 'user1' // In real app, get from auth
    const isPublic = searchParams.get('public') === 'true'
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100)
    const offset = parseInt(searchParams.get('offset') || '0')

    // Filter sessions based on user access
    let filteredSessions = sessions.filter(session => {
      if (isPublic) {
        return session.isPublic
      }
      return session.participants.some(p => p.id === userId) || session.isPublic
    })

    // Apply pagination
    const paginatedSessions = filteredSessions.slice(offset, offset + limit)

    return NextResponse.json({
      success: true,
      data: {
        sessions: paginatedSessions,
        total: filteredSessions.length,
        limit,
        offset,
        hasMore: offset + limit < filteredSessions.length
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Sessions list error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/collaboration/sessions - Create new session
export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitKey = getRateLimitKey(request)
    if (!checkRateLimit(rateLimitKey, 5)) { // Lower limit for creation
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { name, description, isPublic = false, settings } = body

    // Validation
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json(
        { error: 'Session name is required' },
        { status: 400 }
      )
    }

    if (name.length > 100) {
      return NextResponse.json(
        { error: 'Session name too long (max 100 characters)' },
        { status: 400 }
      )
    }

    if (description && description.length > 500) {
      return NextResponse.json(
        { error: 'Description too long (max 500 characters)' },
        { status: 400 }
      )
    }

    // Create new session
    const sessionId = Date.now().toString()
    const currentUserId = 'user1' // In real app, get from auth
    const now = new Date().toISOString()

    const newSession: CollaborationSession = {
      id: sessionId,
      name: name.trim(),
      description: description?.trim() || '',
      createdAt: now,
      lastModified: now,
      ownerId: currentUserId,
      participants: [
        {
          id: currentUserId,
          name: 'Current User', // In real app, get from user profile
          email: '<EMAIL>',
          role: 'owner',
          joinedAt: now,
          isActive: true
        }
      ],
      isPublic: Boolean(isPublic),
      shareLink: `https://formulaforge.com/session/${sessionId}`,
      documentCount: 0,
      settings: {
        allowAnonymous: settings?.allowAnonymous || false,
        maxParticipants: Math.min(settings?.maxParticipants || 10, 50),
        permissions: {
          canEdit: settings?.permissions?.canEdit !== false,
          canComment: settings?.permissions?.canComment !== false,
          canShare: settings?.permissions?.canShare !== false
        }
      }
    }

    sessions.unshift(newSession)

    return NextResponse.json({
      success: true,
      data: newSession,
      timestamp: new Date().toISOString()
    }, { status: 201 })

  } catch (error) {
    console.error('Session creation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/collaboration/sessions - Delete session
export async function DELETE(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitKey = getRateLimitKey(request)
    if (!checkRateLimit(rateLimitKey, 10)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('id')
    const userId = searchParams.get('userId') || 'user1'

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      )
    }

    const sessionIndex = sessions.findIndex(s => s.id === sessionId)
    if (sessionIndex === -1) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      )
    }

    const session = sessions[sessionIndex]
    
    // Check permissions
    if (session.ownerId !== userId) {
      return NextResponse.json(
        { error: 'Only the session owner can delete the session' },
        { status: 403 }
      )
    }

    // Remove session
    sessions.splice(sessionIndex, 1)

    return NextResponse.json({
      success: true,
      message: 'Session deleted successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Session deletion error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
