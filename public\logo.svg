<svg width="200" height="60" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- FormulaForge Logo -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Mathematical Symbol Background -->
  <circle cx="30" cy="30" r="25" fill="url(#logoGradient)" opacity="0.1"/>
  
  <!-- Integral Symbol -->
  <path d="M20 15 Q25 10 30 15 L30 45 Q25 50 20 45" stroke="url(#logoGradient)" stroke-width="3" fill="none"/>
  
  <!-- Sigma Symbol -->
  <path d="M35 20 L45 20 L40 30 L45 40 L35 40 L35 38 L42 38 L38 30 L42 22 L35 22 Z" fill="url(#logoGradient)"/>
  
  <!-- Text -->
  <text x="55" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1f2937">
    Formula
  </text>
  <text x="55" y="45" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="url(#logoGradient)">
    Forge
  </text>
  
  <!-- Mathematical Accent -->
  <circle cx="170" cy="15" r="2" fill="url(#logoGradient)"/>
  <circle cx="180" cy="20" r="1.5" fill="url(#logoGradient)" opacity="0.7"/>
  <circle cx="175" cy="25" r="1" fill="url(#logoGradient)" opacity="0.5"/>
</svg>
