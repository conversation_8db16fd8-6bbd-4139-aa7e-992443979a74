import { evaluate, parse, simplify, derivative, format, MathNode } from 'mathjs'
import { Matrix } from 'ml-matrix'
import * as stats from 'simple-statistics'

export interface MathResult {
  result: any
  steps?: string[]
  latex?: string
  simplified?: string
  error?: string
  type: 'number' | 'expression' | 'matrix' | 'function' | 'error'
}

export interface EquationSolution {
  variable: string
  solutions: number[]
  steps: string[]
  method: string
}

export interface DerivativeResult {
  original: string
  derivative: string
  latex: string
  steps: string[]
}

export interface IntegralResult {
  original: string
  integral: string
  latex: string
  definite?: number
  steps: string[]
}

export class MathematicalEngine {
  private precision: number = 14

  constructor(precision: number = 14) {
    this.precision = precision
  }

  /**
   * Evaluate a mathematical expression
   */
  evaluate(expression: string, variables: Record<string, number> = {}): MathResult {
    try {
      // Parse the expression
      const node = parse(expression)
      
      // Evaluate with variables
      const result = node.evaluate(variables)
      
      // Simplify if possible
      let simplified: string | undefined
      try {
        const simplifiedNode = simplify(node)
        simplified = simplifiedNode.toString()
      } catch {
        // Simplification failed, continue without it
      }

      return {
        result,
        simplified,
        latex: this.toLatex(expression),
        type: this.getResultType(result),
      }
    } catch (error) {
      return {
        result: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        type: 'error',
      }
    }
  }

  /**
   * Solve equations step by step
   */
  solveEquation(equation: string, variable: string = 'x'): EquationSolution {
    const steps: string[] = []
    steps.push(`Original equation: ${equation}`)

    try {
      // Parse the equation (assuming it's in the form "expression = 0")
      let expr = equation.replace(/=/g, '-(') + ')'
      steps.push(`Rearranged to: ${expr} = 0`)

      // For simple linear equations
      if (this.isLinear(expr, variable)) {
        return this.solveLinear(expr, variable, steps)
      }

      // For quadratic equations
      if (this.isQuadratic(expr, variable)) {
        return this.solveQuadratic(expr, variable, steps)
      }

      // For other equations, use numerical methods
      return this.solveNumerical(expr, variable, steps)

    } catch (error) {
      return {
        variable,
        solutions: [],
        steps: [...steps, `Error: ${error instanceof Error ? error.message : 'Unknown error'}`],
        method: 'error',
      }
    }
  }

  /**
   * Calculate symbolic derivative
   */
  calculateDerivative(expression: string, variable: string = 'x'): DerivativeResult {
    const steps: string[] = []
    steps.push(`Finding derivative of ${expression} with respect to ${variable}`)

    try {
      const node = parse(expression)
      const derivativeNode = derivative(node, variable)
      const derivativeStr = derivativeNode.toString()

      // Add step-by-step explanation based on rules
      this.addDerivativeSteps(expression, variable, steps)

      return {
        original: expression,
        derivative: derivativeStr,
        latex: this.toLatex(derivativeStr),
        steps,
      }
    } catch (error) {
      steps.push(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return {
        original: expression,
        derivative: '',
        latex: '',
        steps,
      }
    }
  }

  /**
   * Calculate symbolic integral (basic implementation)
   */
  calculateIntegral(expression: string, variable: string = 'x', limits?: [number, number]): IntegralResult {
    const steps: string[] = []
    steps.push(`Finding integral of ${expression} with respect to ${variable}`)

    try {
      // Basic integration rules implementation
      const integral = this.integrateBasic(expression, variable, steps)
      
      let definiteValue: number | undefined
      if (limits && integral) {
        try {
          const [a, b] = limits
          const F_b = evaluate(integral, { [variable]: b })
          const F_a = evaluate(integral, { [variable]: a })
          definiteValue = F_b - F_a
          steps.push(`Evaluating definite integral from ${a} to ${b}`)
          steps.push(`F(${b}) - F(${a}) = ${F_b} - ${F_a} = ${definiteValue}`)
        } catch {
          steps.push('Could not evaluate definite integral')
        }
      }

      return {
        original: expression,
        integral: integral || '',
        latex: integral ? this.toLatex(integral) : '',
        definite: definiteValue,
        steps,
      }
    } catch (error) {
      steps.push(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      return {
        original: expression,
        integral: '',
        latex: '',
        steps,
      }
    }
  }

  /**
   * Perform matrix operations
   */
  matrixOperations(operation: string, matrices: number[][][]): MathResult {
    try {
      const mlMatrices = matrices.map(m => new Matrix(m))
      let result: Matrix

      switch (operation.toLowerCase()) {
        case 'add':
          result = Matrix.add(mlMatrices[0], mlMatrices[1])
          break
        case 'subtract':
          result = Matrix.subtract(mlMatrices[0], mlMatrices[1])
          break
        case 'multiply':
          result = mlMatrices[0].mmul(mlMatrices[1])
          break
        case 'transpose':
          result = mlMatrices[0].transpose()
          break
        case 'inverse':
          result = mlMatrices[0].inverse()
          break
        case 'determinant':
          return {
            result: mlMatrices[0].det(),
            type: 'number',
          }
        case 'eigenvalues':
          const eig = mlMatrices[0].eig()
          return {
            result: eig.realEigenvalues,
            type: 'number',
          }
        default:
          throw new Error(`Unknown matrix operation: ${operation}`)
      }

      return {
        result: result.to2DArray(),
        type: 'matrix',
      }
    } catch (error) {
      return {
        result: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        type: 'error',
      }
    }
  }

  /**
   * Statistical analysis
   */
  statisticalAnalysis(data: number[], operation: string): MathResult {
    try {
      let result: number | number[]

      switch (operation.toLowerCase()) {
        case 'mean':
          result = stats.mean(data)
          break
        case 'median':
          result = stats.median(data)
          break
        case 'mode':
          result = stats.mode(data)
          break
        case 'standarddeviation':
          result = stats.standardDeviation(data)
          break
        case 'variance':
          result = stats.variance(data)
          break
        case 'regression':
          // Assuming x values are indices
          const xValues = data.map((_, i) => i)
          const regression = stats.linearRegression(xValues.map((x, i) => [x, data[i]]))
          result = [regression.m, regression.b] // slope, intercept
          break
        default:
          throw new Error(`Unknown statistical operation: ${operation}`)
      }

      return {
        result,
        type: 'number',
      }
    } catch (error) {
      return {
        result: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        type: 'error',
      }
    }
  }

  // Private helper methods

  private getResultType(result: any): 'number' | 'expression' | 'matrix' | 'function' | 'error' {
    if (typeof result === 'number') return 'number'
    if (Array.isArray(result)) return 'matrix'
    if (typeof result === 'function') return 'function'
    return 'expression'
  }

  private isLinear(expr: string, variable: string): boolean {
    try {
      const node = parse(expr)
      const secondDerivative = derivative(derivative(node, variable), variable)
      return secondDerivative.toString() === '0'
    } catch {
      return false
    }
  }

  private isQuadratic(expr: string, variable: string): boolean {
    try {
      const node = parse(expr)
      const thirdDerivative = derivative(derivative(derivative(node, variable), variable), variable)
      return thirdDerivative.toString() === '0'
    } catch {
      return false
    }
  }

  private solveLinear(expr: string, variable: string, steps: string[]): EquationSolution {
    // ax + b = 0 => x = -b/a
    try {
      const node = parse(expr)
      const a = derivative(node, variable).evaluate({ [variable]: 0 })
      const b = node.evaluate({ [variable]: 0 })
      
      const solution = -b / a
      steps.push(`Linear equation: a${variable} + b = 0`)
      steps.push(`a = ${a}, b = ${b}`)
      steps.push(`${variable} = -b/a = ${-b}/${a} = ${solution}`)

      return {
        variable,
        solutions: [solution],
        steps,
        method: 'linear',
      }
    } catch (error) {
      return {
        variable,
        solutions: [],
        steps: [...steps, `Error solving linear equation: ${error}`],
        method: 'error',
      }
    }
  }

  private solveQuadratic(expr: string, variable: string, steps: string[]): EquationSolution {
    // Quadratic formula implementation would go here
    // This is a simplified version
    steps.push('Using quadratic formula (simplified implementation)')
    return {
      variable,
      solutions: [],
      steps: [...steps, 'Quadratic solver not fully implemented'],
      method: 'quadratic',
    }
  }

  private solveNumerical(expr: string, variable: string, steps: string[]): EquationSolution {
    // Newton-Raphson method implementation would go here
    steps.push('Using numerical methods (simplified implementation)')
    return {
      variable,
      solutions: [],
      steps: [...steps, 'Numerical solver not fully implemented'],
      method: 'numerical',
    }
  }

  private addDerivativeSteps(expression: string, variable: string, steps: string[]): void {
    // Add basic derivative rules explanation
    if (expression.includes('^')) {
      steps.push('Using power rule: d/dx[x^n] = n*x^(n-1)')
    }
    if (expression.includes('sin') || expression.includes('cos')) {
      steps.push('Using trigonometric derivatives')
    }
    if (expression.includes('ln') || expression.includes('log')) {
      steps.push('Using logarithmic derivatives')
    }
  }

  private integrateBasic(expression: string, variable: string, steps: string[]): string | null {
    // Basic integration rules
    if (expression === variable) {
      steps.push(`∫${variable} d${variable} = ${variable}²/2 + C`)
      return `${variable}^2/2`
    }
    
    if (expression === '1') {
      steps.push(`∫1 d${variable} = ${variable} + C`)
      return variable
    }

    // Power rule: ∫x^n dx = x^(n+1)/(n+1) + C
    const powerMatch = expression.match(new RegExp(`${variable}\\^(\\d+)`))
    if (powerMatch) {
      const n = parseInt(powerMatch[1])
      const newPower = n + 1
      steps.push(`Using power rule: ∫${variable}^${n} d${variable} = ${variable}^${newPower}/${newPower} + C`)
      return `${variable}^${newPower}/${newPower}`
    }

    steps.push('Integration rule not implemented for this expression')
    return null
  }

  private toLatex(expression: string): string {
    return expression
      .replace(/\*/g, ' \\cdot ')
      .replace(/sqrt\(([^)]+)\)/g, '\\sqrt{$1}')
      .replace(/\^([0-9]+)/g, '^{$1}')
      .replace(/pi/g, '\\pi')
      .replace(/infinity/g, '\\infty')
  }
}
