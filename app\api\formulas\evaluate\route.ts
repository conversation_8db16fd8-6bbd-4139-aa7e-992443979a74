import { NextRequest, NextResponse } from 'next/server'
import { evaluate, parse, simplify } from 'mathjs'

interface EvaluationRequest {
  formula: string
  variables?: Record<string, number>
  operation?: 'evaluate' | 'simplify' | 'derivative' | 'integral'
  options?: {
    precision?: number
    format?: 'number' | 'fraction' | 'expression'
  }
}

interface EvaluationResult {
  result: any
  originalFormula: string
  processedFormula?: string
  variables?: Record<string, number>
  steps?: string[]
  warnings?: string[]
}

// Rate limiting
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

function getRateLimitKey(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : 'unknown'
  return ip
}

function checkRateLimit(key: string, limit: number = 20, windowMs: number = 60000): boolean {
  const now = Date.now()
  const record = rateLimitMap.get(key)
  
  if (!record || now > record.resetTime) {
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (record.count >= limit) {
    return false
  }
  
  record.count++
  return true
}

function sanitizeFormula(formula: string): string {
  // Remove potentially dangerous functions and operations
  const dangerous = [
    'import', 'require', 'eval', 'Function', 'setTimeout', 'setInterval',
    'process', 'global', 'window', 'document', 'console'
  ]
  
  let sanitized = formula
  dangerous.forEach(term => {
    const regex = new RegExp(term, 'gi')
    if (regex.test(sanitized)) {
      throw new Error(`Potentially unsafe operation detected: ${term}`)
    }
  })
  
  return sanitized
}

function validateVariables(variables: Record<string, number>): void {
  for (const [key, value] of Object.entries(variables)) {
    if (typeof value !== 'number' || !isFinite(value)) {
      throw new Error(`Invalid variable value for ${key}: must be a finite number`)
    }
    if (Math.abs(value) > 1e10) {
      throw new Error(`Variable value for ${key} is too large`)
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitKey = getRateLimitKey(request)
    if (!checkRateLimit(rateLimitKey)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { 
      formula, 
      variables = {}, 
      operation = 'evaluate',
      options = {}
    }: EvaluationRequest = body

    // Validation
    if (!formula || typeof formula !== 'string') {
      return NextResponse.json(
        { error: 'Formula is required and must be a string' },
        { status: 400 }
      )
    }

    if (formula.length > 500) {
      return NextResponse.json(
        { error: 'Formula too long. Please keep it under 500 characters.' },
        { status: 400 }
      )
    }

    // Sanitize inputs
    const sanitizedFormula = sanitizeFormula(formula)
    validateVariables(variables)

    const result: EvaluationResult = {
      result: null,
      originalFormula: formula,
      variables,
      steps: [],
      warnings: []
    }

    try {
      // Parse the formula first to check syntax
      const parsed = parse(sanitizedFormula)
      result.steps?.push('Formula parsed successfully')

      switch (operation) {
        case 'evaluate':
          if (Object.keys(variables).length > 0) {
            result.result = evaluate(sanitizedFormula, variables)
            result.steps?.push(`Evaluated with variables: ${JSON.stringify(variables)}`)
          } else {
            result.result = evaluate(sanitizedFormula)
            result.steps?.push('Evaluated without variables')
          }
          break

        case 'simplify':
          const simplified = simplify(parsed)
          result.result = simplified.toString()
          result.processedFormula = result.result
          result.steps?.push('Formula simplified')
          break

        case 'derivative':
          // Basic derivative calculation (limited functionality)
          try {
            const derivative = parse(sanitizedFormula).transform((node: any) => {
              // This is a very basic implementation
              // In a real app, you'd use a proper symbolic math library
              return node
            })
            result.result = 'Derivative calculation requires advanced symbolic math library'
            result.warnings?.push('Derivative calculation is not fully implemented')
          } catch (err) {
            throw new Error('Derivative calculation failed')
          }
          break

        case 'integral':
          result.result = 'Integral calculation requires advanced symbolic math library'
          result.warnings?.push('Integral calculation is not fully implemented')
          break

        default:
          throw new Error(`Unsupported operation: ${operation}`)
      }

      // Format result based on options
      if (options.format === 'fraction' && typeof result.result === 'number') {
        // Simple fraction conversion (limited)
        const precision = options.precision || 6
        result.result = Number(result.result.toFixed(precision))
      }

      // Add warnings for edge cases
      if (typeof result.result === 'number') {
        if (!isFinite(result.result)) {
          result.warnings?.push('Result is infinite or undefined')
        }
        if (Math.abs(result.result) > 1e15) {
          result.warnings?.push('Result is very large and may have precision issues')
        }
      }

      return NextResponse.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })

    } catch (mathError) {
      return NextResponse.json(
        { 
          error: 'Mathematical evaluation failed',
          details: mathError instanceof Error ? mathError.message : 'Unknown math error',
          formula: sanitizedFormula
        },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Formula evaluation error:', error)
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Formula Evaluation API',
    version: '1.0.0',
    endpoints: {
      POST: '/api/formulas/evaluate - Evaluate mathematical formulas',
    },
    operations: ['evaluate', 'simplify', 'derivative', 'integral'],
    rateLimit: {
      requests: 20,
      window: '1 minute'
    },
    examples: {
      evaluate: {
        formula: '2 * x + 3',
        variables: { x: 5 },
        operation: 'evaluate'
      },
      simplify: {
        formula: 'x^2 + 2*x + 1',
        operation: 'simplify'
      }
    }
  })
}
