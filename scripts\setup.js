#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 Setting up FormulaForge...\n')

// Check Node.js version
const nodeVersion = process.version
const requiredVersion = '18.0.0'
console.log(`📋 Node.js version: ${nodeVersion}`)

if (parseInt(nodeVersion.slice(1)) < parseInt(requiredVersion)) {
  console.error(`❌ Node.js ${requiredVersion} or higher is required`)
  process.exit(1)
}

// Create necessary directories
const directories = [
  'public/images',
  'public/icons',
  'public/fonts',
  'database/migrations',
  'database/seeds',
  'docs/api',
  'docs/guides',
  'scripts/deployment',
  'tests/unit',
  'tests/integration',
  'tests/e2e',
]

console.log('📁 Creating directory structure...')
directories.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir)
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true })
    console.log(`   ✅ Created ${dir}`)
  } else {
    console.log(`   ⏭️  ${dir} already exists`)
  }
})

// Create environment file if it doesn't exist
const envPath = path.join(process.cwd(), '.env.local')
const envExamplePath = path.join(process.cwd(), '.env.example')

if (!fs.existsSync(envPath) && fs.existsSync(envExamplePath)) {
  console.log('\n🔧 Setting up environment variables...')
  fs.copyFileSync(envExamplePath, envPath)
  console.log('   ✅ Created .env.local from .env.example')
  console.log('   ⚠️  Please update .env.local with your actual API keys')
} else if (fs.existsSync(envPath)) {
  console.log('   ⏭️  .env.local already exists')
}

// Create additional configuration files
const configFiles = [
  {
    name: 'postcss.config.js',
    content: `module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}
`
  },
  {
    name: 'components.json',
    content: `{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "default",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "app/globals.css",
    "baseColor": "slate",
    "cssVariables": true
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils"
  }
}
`
  },
  {
    name: '.gitignore',
    content: `# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local
.env

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db

# Logs
logs
*.log

# Database
*.db
*.sqlite

# Cache
.cache/
.parcel-cache/

# Temporary files
.tmp/
temp/
`
  },
  {
    name: 'jest.config.js',
    content: `const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    // Handle module aliases (this will be automatically configured for you based on your tsconfig.json paths)
    '^@/(.*)$': '<rootDir>/$1',
  },
  testEnvironment: 'jest-environment-jsdom',
  collectCoverageFrom: [
    'components/**/*.{js,jsx,ts,tsx}',
    'lib/**/*.{js,jsx,ts,tsx}',
    'backend/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
  ],
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig)
`
  },
  {
    name: 'jest.setup.js',
    content: `import '@testing-library/jest-dom'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return ''
  },
}))

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props) => {
    return <img {...props} />
  },
}))

// Mock environment variables
process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000'
`
  },
  {
    name: '.eslintrc.json',
    content: `{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": ["@typescript-eslint"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "error",
    "no-var": "error"
  },
  "ignorePatterns": ["node_modules/", ".next/", "out/"]
}
`
  },
  {
    name: '.prettierrc',
    content: `{
  "semi": false,
  "trailingComma": "es5",
  "singleQuote": true,
  "tabWidth": 2,
  "useTabs": false,
  "printWidth": 100,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
`
  }
]

console.log('\n📝 Creating configuration files...')
configFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file.name)
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, file.content)
    console.log(`   ✅ Created ${file.name}`)
  } else {
    console.log(`   ⏭️  ${file.name} already exists`)
  }
})

// Create sample data files
const sampleFiles = [
  {
    name: 'public/manifest.json',
    content: `{
  "name": "FormulaForge",
  "short_name": "FormulaForge",
  "description": "Advanced Mathematical Formula Generator",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#0ea5e9",
  "icons": [
    {
      "src": "/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icon-512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
`
  },
  {
    name: 'public/robots.txt',
    content: `User-agent: *
Allow: /

Sitemap: https://formulaforge.com/sitemap.xml
`
  }
]

console.log('\n📄 Creating sample files...')
sampleFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file.name)
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, file.content)
    console.log(`   ✅ Created ${file.name}`)
  } else {
    console.log(`   ⏭️  ${file.name} already exists`)
  }
})

console.log('\n🎉 Setup complete!')
console.log('\n📋 Next steps:')
console.log('   1. Update .env.local with your API keys')
console.log('   2. Run "npm install" to install dependencies')
console.log('   3. Run "npm run dev" to start the development server')
console.log('   4. Visit http://localhost:3000 to see your app')
console.log('\n📚 Documentation:')
console.log('   - README.md for project overview')
console.log('   - docs/ folder for detailed guides')
console.log('   - .env.example for environment variable reference')
console.log('\n🚀 Happy coding!')
