-- FormulaForge Sample Data
-- Seed data for development and testing

-- Sample formula categories and popular formulas
INSERT INTO formula_library (id, name, description, formula, category, tags, variables, examples, is_public, created_by) VALUES
(
  'formula_quadratic_001',
  'Quadratic Formula',
  'Solves quadratic equations of the form ax² + bx + c = 0',
  '(-b ± sqrt(b^2 - 4*a*c)) / (2*a)',
  'algebra',
  ARRAY['quadratic', 'equation', 'polynomial', 'roots'],
  '[{"name": "a", "description": "Coefficient of x²", "type": "number"}, {"name": "b", "description": "Coefficient of x", "type": "number"}, {"name": "c", "description": "Constant term", "type": "number"}]'::jsonb,
  '[{"input": {"a": 1, "b": -5, "c": 6}, "output": "x = 2 or x = 3", "description": "Find roots of x² - 5x + 6 = 0"}]'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'formula_distance_001',
  'Distance Formula',
  'Calculates the distance between two points in 2D space',
  'sqrt((x2-x1)^2 + (y2-y1)^2)',
  'geometry',
  ARRAY['distance', 'geometry', 'coordinates', '2d'],
  '[{"name": "x1", "description": "X coordinate of first point", "type": "number"}, {"name": "y1", "description": "Y coordinate of first point", "type": "number"}, {"name": "x2", "description": "X coordinate of second point", "type": "number"}, {"name": "y2", "description": "Y coordinate of second point", "type": "number"}]'::jsonb,
  '[{"input": {"x1": 0, "y1": 0, "x2": 3, "y2": 4}, "output": "5", "description": "Distance from origin to point (3,4)"}]'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'formula_circle_area_001',
  'Circle Area',
  'Calculates the area of a circle given its radius',
  'pi * r^2',
  'geometry',
  ARRAY['circle', 'area', 'radius', 'geometry'],
  '[{"name": "r", "description": "Radius of the circle", "type": "number", "constraints": {"min": 0}}]'::jsonb,
  '[{"input": {"r": 5}, "output": "78.54", "description": "Area of circle with radius 5"}]'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'formula_compound_interest_001',
  'Compound Interest',
  'Calculates compound interest with principal, rate, time, and compounding frequency',
  'P * (1 + r/n)^(n*t)',
  'finance',
  ARRAY['finance', 'interest', 'investment', 'compound'],
  '[{"name": "P", "description": "Principal amount", "type": "number"}, {"name": "r", "description": "Annual interest rate (decimal)", "type": "number"}, {"name": "n", "description": "Number of times interest compounds per year", "type": "number"}, {"name": "t", "description": "Time in years", "type": "number"}]'::jsonb,
  '[{"input": {"P": 1000, "r": 0.05, "n": 12, "t": 3}, "output": "1161.62", "description": "$1000 at 5% compounded monthly for 3 years"}]'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'formula_pythagorean_001',
  'Pythagorean Theorem',
  'Relates the lengths of sides in a right triangle',
  'sqrt(a^2 + b^2)',
  'geometry',
  ARRAY['pythagorean', 'triangle', 'right-triangle', 'hypotenuse'],
  '[{"name": "a", "description": "Length of first leg", "type": "number"}, {"name": "b", "description": "Length of second leg", "type": "number"}]'::jsonb,
  '[{"input": {"a": 3, "b": 4}, "output": "5", "description": "Hypotenuse of 3-4-5 triangle"}]'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'formula_slope_001',
  'Slope Formula',
  'Calculates the slope of a line between two points',
  '(y2 - y1) / (x2 - x1)',
  'algebra',
  ARRAY['slope', 'line', 'linear', 'coordinates'],
  '[{"name": "x1", "description": "X coordinate of first point", "type": "number"}, {"name": "y1", "description": "Y coordinate of first point", "type": "number"}, {"name": "x2", "description": "X coordinate of second point", "type": "number"}, {"name": "y2", "description": "Y coordinate of second point", "type": "number"}]'::jsonb,
  '[{"input": {"x1": 0, "y1": 0, "x2": 2, "y2": 4}, "output": "2", "description": "Slope of line from origin to (2,4)"}]'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'formula_kinetic_energy_001',
  'Kinetic Energy',
  'Calculates the kinetic energy of an object in motion',
  '0.5 * m * v^2',
  'physics',
  ARRAY['physics', 'energy', 'kinetic', 'motion'],
  '[{"name": "m", "description": "Mass of the object (kg)", "type": "number"}, {"name": "v", "description": "Velocity of the object (m/s)", "type": "number"}]'::jsonb,
  '[{"input": {"m": 10, "v": 5}, "output": "125", "description": "Kinetic energy of 10kg object moving at 5 m/s"}]'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'formula_standard_deviation_001',
  'Standard Deviation',
  'Measures the amount of variation in a dataset',
  'sqrt(sum((x - mean)^2) / (n - 1))',
  'statistics',
  ARRAY['statistics', 'deviation', 'variance', 'data'],
  '[{"name": "x", "description": "Data values", "type": "array"}, {"name": "mean", "description": "Mean of the data", "type": "number"}, {"name": "n", "description": "Number of data points", "type": "number"}]'::jsonb,
  '[{"input": {"x": [1,2,3,4,5], "mean": 3, "n": 5}, "output": "1.58", "description": "Standard deviation of [1,2,3,4,5]"}]'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'formula_bmi_001',
  'Body Mass Index (BMI)',
  'Calculates BMI based on weight and height',
  'weight / (height^2)',
  'custom',
  ARRAY['health', 'bmi', 'weight', 'height'],
  '[{"name": "weight", "description": "Weight in kilograms", "type": "number"}, {"name": "height", "description": "Height in meters", "type": "number"}]'::jsonb,
  '[{"input": {"weight": 70, "height": 1.75}, "output": "22.86", "description": "BMI for 70kg person, 1.75m tall"}]'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'formula_ohms_law_001',
  'Ohm\'s Law',
  'Relates voltage, current, and resistance in electrical circuits',
  'V / R',
  'physics',
  ARRAY['physics', 'electricity', 'ohm', 'current', 'voltage', 'resistance'],
  '[{"name": "V", "description": "Voltage in volts", "type": "number"}, {"name": "R", "description": "Resistance in ohms", "type": "number"}]'::jsonb,
  '[{"input": {"V": 12, "R": 4}, "output": "3", "description": "Current through 4Ω resistor with 12V applied"}]'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
);

-- Sample collaboration rooms
INSERT INTO collaboration_rooms (id, name, description, is_public, max_participants, created_by) VALUES
(
  'room_calculus_study_001',
  'Calculus Study Group',
  'A collaborative space for studying calculus concepts and solving problems together',
  true,
  15,
  '00000000-0000-0000-0000-000000000000'
),
(
  'room_physics_formulas_001',
  'Physics Formula Workshop',
  'Explore and derive physics formulas collaboratively',
  true,
  20,
  '00000000-0000-0000-0000-000000000000'
),
(
  'room_engineering_math_001',
  'Engineering Mathematics',
  'Advanced mathematical concepts for engineering applications',
  true,
  25,
  '00000000-0000-0000-0000-000000000000'
),
(
  'room_statistics_lab_001',
  'Statistics Laboratory',
  'Collaborative statistical analysis and formula development',
  true,
  12,
  '00000000-0000-0000-0000-000000000000'
);

-- Sample workspaces
INSERT INTO workspaces (name, description, data, is_public, created_by) VALUES
(
  'Geometry Fundamentals',
  'Basic geometric formulas and theorems',
  '{"formulas": ["formula_distance_001", "formula_circle_area_001", "formula_pythagorean_001"], "notes": "Collection of fundamental geometry formulas", "tags": ["geometry", "basics"]}'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'Physics Mechanics',
  'Classical mechanics formulas and equations',
  '{"formulas": ["formula_kinetic_energy_001"], "notes": "Mechanics formulas for introductory physics", "tags": ["physics", "mechanics"]}'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'Financial Calculations',
  'Common financial and investment formulas',
  '{"formulas": ["formula_compound_interest_001"], "notes": "Essential formulas for financial calculations", "tags": ["finance", "investment"]}'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
),
(
  'Statistical Analysis Toolkit',
  'Statistical formulas and data analysis tools',
  '{"formulas": ["formula_standard_deviation_001"], "notes": "Statistical formulas for data analysis", "tags": ["statistics", "data"]}'::jsonb,
  true,
  '00000000-0000-0000-0000-000000000000'
);

-- Sample formula ratings
INSERT INTO formula_ratings (formula_id, user_id, rating, review) VALUES
('formula_quadratic_001', '00000000-0000-0000-0000-000000000000', 5, 'Essential formula for algebra. Clear and well-documented.'),
('formula_distance_001', '00000000-0000-0000-0000-000000000000', 5, 'Perfect for coordinate geometry problems.'),
('formula_circle_area_001', '00000000-0000-0000-0000-000000000000', 5, 'Simple and fundamental. Great examples provided.'),
('formula_compound_interest_001', '00000000-0000-0000-0000-000000000000', 4, 'Very useful for financial calculations. Could use more examples.'),
('formula_pythagorean_001', '00000000-0000-0000-0000-000000000000', 5, 'Classic theorem. Well implemented.'),
('formula_slope_001', '00000000-0000-0000-0000-000000000000', 4, 'Good for linear algebra. Clear variable descriptions.'),
('formula_kinetic_energy_001', '00000000-0000-0000-0000-000000000000', 5, 'Essential physics formula. Great for mechanics problems.'),
('formula_standard_deviation_001', '00000000-0000-0000-0000-000000000000', 4, 'Important statistical measure. Implementation is solid.'),
('formula_bmi_001', '00000000-0000-0000-0000-000000000000', 3, 'Useful health calculation. Simple and straightforward.'),
('formula_ohms_law_001', '00000000-0000-0000-0000-000000000000', 5, 'Fundamental electrical engineering formula. Well documented.');

-- Update usage counts for popular formulas
UPDATE formula_library SET usage_count = 1250 WHERE id = 'formula_quadratic_001';
UPDATE formula_library SET usage_count = 980 WHERE id = 'formula_distance_001';
UPDATE formula_library SET usage_count = 1100 WHERE id = 'formula_circle_area_001';
UPDATE formula_library SET usage_count = 750 WHERE id = 'formula_compound_interest_001';
UPDATE formula_library SET usage_count = 890 WHERE id = 'formula_pythagorean_001';
UPDATE formula_library SET usage_count = 650 WHERE id = 'formula_slope_001';
UPDATE formula_library SET usage_count = 820 WHERE id = 'formula_kinetic_energy_001';
UPDATE formula_library SET usage_count = 540 WHERE id = 'formula_standard_deviation_001';
UPDATE formula_library SET usage_count = 320 WHERE id = 'formula_bmi_001';
UPDATE formula_library SET usage_count = 710 WHERE id = 'formula_ohms_law_001';

-- Sample computation history (for demonstration)
INSERT INTO computation_history (user_id, input_text, formula, operation_type, result, confidence_score, processing_time_ms) VALUES
(
  '00000000-0000-0000-0000-000000000000',
  'solve x squared minus 5x plus 6 equals 0',
  'x^2 - 5*x + 6 = 0',
  'solve_equation',
  '{"solutions": [2, 3], "steps": ["Factor: (x-2)(x-3) = 0", "Set each factor to zero", "x = 2 or x = 3"]}'::jsonb,
  0.95,
  245
),
(
  '00000000-0000-0000-0000-000000000000',
  'find the derivative of x cubed plus 2x squared',
  'x^3 + 2*x^2',
  'derivative',
  '{"derivative": "3*x^2 + 4*x", "steps": ["d/dx(x^3) = 3*x^2", "d/dx(2*x^2) = 4*x", "Sum: 3*x^2 + 4*x"]}'::jsonb,
  0.98,
  180
),
(
  '00000000-0000-0000-0000-000000000000',
  'what is the area of a circle with radius 7',
  'pi * r^2',
  'evaluate',
  '{"result": 153.938, "formula": "π × 7² = 153.938", "units": "square units"}'::jsonb,
  0.99,
  95
);

-- Add some API usage data for analytics
INSERT INTO api_usage (user_id, endpoint, method, status_code, response_time_ms, request_size_bytes, response_size_bytes, ip_address) VALUES
('00000000-0000-0000-0000-000000000000', '/api/nlp/process', 'POST', 200, 245, 156, 1024, '127.0.0.1'),
('00000000-0000-0000-0000-000000000000', '/api/math/evaluate', 'POST', 200, 180, 89, 512, '127.0.0.1'),
('00000000-0000-0000-0000-000000000000', '/api/formulas/library', 'GET', 200, 95, 0, 2048, '127.0.0.1'),
('00000000-0000-0000-0000-000000000000', '/api/collaboration/rooms', 'GET', 200, 120, 0, 1536, '127.0.0.1'),
('00000000-0000-0000-0000-000000000000', '/api/export/code', 'POST', 200, 340, 234, 1890, '127.0.0.1');
