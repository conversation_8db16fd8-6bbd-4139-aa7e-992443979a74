'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './graph-renderer'
import { 
  Play, 
  Code, 
  Brain, 
  Calculator, 
  BarChart3, 
  Zap,
  ArrowRight,
  Copy,
  CheckCircle
} from 'lucide-react'

const demoExamples = [
  {
    id: 'quadratic',
    title: 'Quadratic Function',
    description: 'Visualize parabolic curves and their properties',
    formula: 'x^2 - 4*x + 3',
    naturalLanguage: 'Graph the quadratic function x squared minus 4x plus 3',
    type: '2d' as const,
    category: 'Algebra',
    complexity: 'Basic',
  },
  {
    id: 'trigonometric',
    title: 'Trigonometric Wave',
    description: 'Explore sine and cosine wave patterns',
    formula: 'Math.sin(x) + 0.5*Math.cos(2*x)',
    naturalLanguage: 'Show me a sine wave plus half a cosine wave with double frequency',
    type: '2d' as const,
    category: 'Trigonometry',
    complexity: 'Intermediate',
  },
  {
    id: 'exponential',
    title: 'Exponential Growth',
    description: 'Model exponential growth and decay',
    formula: 'Math.exp(x/2)',
    naturalLanguage: 'Plot exponential growth with base e and exponent x over 2',
    type: '2d' as const,
    category: 'Calculus',
    complexity: 'Intermediate',
  },
  {
    id: 'parametric',
    title: 'Parametric Curve',
    description: 'Beautiful parametric equations creating complex shapes',
    formula: 't => [Math.cos(t), Math.sin(t)]',
    naturalLanguage: 'Create a parametric circle using cosine and sine functions',
    type: 'parametric' as const,
    category: 'Geometry',
    complexity: 'Advanced',
  },
  {
    id: 'polar',
    title: 'Polar Rose',
    description: 'Stunning polar coordinate patterns',
    formula: '2*Math.cos(3*x)',
    naturalLanguage: 'Draw a three-petaled rose in polar coordinates',
    type: 'polar' as const,
    category: 'Geometry',
    complexity: 'Advanced',
  },
  {
    id: '3d-surface',
    title: '3D Surface',
    description: 'Three-dimensional mathematical surfaces',
    formula: 'x^2 + y^2',
    naturalLanguage: 'Show a 3D paraboloid surface',
    type: '3d' as const,
    category: 'Multivariable',
    complexity: 'Advanced',
  },
]

const steps = [
  {
    icon: Brain,
    title: 'Natural Language Input',
    description: 'Describe your mathematical problem in plain English',
    color: 'text-blue-500',
  },
  {
    icon: Zap,
    title: 'AI Processing',
    description: 'Our AI converts your description into precise formulas',
    color: 'text-yellow-500',
  },
  {
    icon: Calculator,
    title: 'Mathematical Engine',
    description: 'Advanced algorithms solve and simplify expressions',
    color: 'text-green-500',
  },
  {
    icon: BarChart3,
    title: 'Interactive Visualization',
    description: 'See your results in beautiful, interactive graphs',
    color: 'text-purple-500',
  },
]

export function DemoSection() {
  const [selectedExample, setSelectedExample] = useState(demoExamples[0])
  const [activeStep, setActiveStep] = useState(0)
  const [copied, setCopied] = useState(false)

  const handleCopyFormula = async () => {
    try {
      await navigator.clipboard.writeText(selectedExample.formula)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const handleTryExample = (example: typeof demoExamples[0]) => {
    setSelectedExample(example)
    // Simulate step progression
    setActiveStep(0)
    const stepInterval = setInterval(() => {
      setActiveStep(prev => {
        if (prev >= 3) {
          clearInterval(stepInterval)
          return 3
        }
        return prev + 1
      })
    }, 1000)
  }

  return (
    <section className="py-20 bg-gradient-to-br from-background to-muted/30">
      <div className="container-wide">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">
            <Play className="w-4 h-4 mr-2" />
            Interactive Demo
          </Badge>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            See FormulaForge in Action
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Experience the power of AI-driven mathematical computation. 
            Try these examples or describe your own mathematical problems.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Left Side - Process Steps */}
          <div className="space-y-8">
            <h3 className="text-2xl font-bold mb-6">How It Works</h3>
            
            <div className="space-y-6">
              {steps.map((step, index) => (
                <div
                  key={index}
                  className={`flex items-start gap-4 p-4 rounded-lg transition-all duration-500 ${
                    index <= activeStep
                      ? 'bg-muted/50 border-l-4 border-primary'
                      : 'opacity-50'
                  }`}
                >
                  <div className={`w-12 h-12 rounded-lg bg-background border-2 flex items-center justify-center ${
                    index <= activeStep ? 'border-primary' : 'border-muted'
                  }`}>
                    <step.icon className={`w-6 h-6 ${index <= activeStep ? step.color : 'text-muted-foreground'}`} />
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="font-semibold mb-1">{step.title}</h4>
                    <p className="text-sm text-muted-foreground">{step.description}</p>
                  </div>
                  
                  {index <= activeStep && (
                    <CheckCircle className="w-5 h-5 text-green-500 mt-1" />
                  )}
                </div>
              ))}
            </div>

            {/* Example Selection */}
            <div className="space-y-4">
              <h4 className="font-semibold">Try These Examples:</h4>
              <div className="grid gap-3">
                {demoExamples.slice(0, 3).map((example) => (
                  <button
                    key={example.id}
                    onClick={() => handleTryExample(example)}
                    className={`text-left p-4 rounded-lg border transition-all hover:shadow-md ${
                      selectedExample.id === example.id
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-medium">{example.title}</h5>
                      <div className="flex gap-2">
                        <Badge variant="outline" className="text-xs">
                          {example.category}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          {example.complexity}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {example.description}
                    </p>
                    <p className="text-xs font-mono bg-muted/50 p-2 rounded">
                      "{example.naturalLanguage}"
                    </p>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Right Side - Live Demo */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="w-5 h-5" />
                      {selectedExample.title}
                    </CardTitle>
                    <CardDescription>{selectedExample.description}</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Badge variant="outline">{selectedExample.category}</Badge>
                    <Badge variant="secondary">{selectedExample.complexity}</Badge>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Natural Language Input */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Natural Language Input:</label>
                  <div className="p-3 bg-muted/50 rounded-lg text-sm">
                    "{selectedExample.naturalLanguage}"
                  </div>
                </div>

                {/* Generated Formula */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Generated Formula:</label>
                  <div className="flex items-center gap-2">
                    <code className="flex-1 p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg text-sm font-mono">
                      {selectedExample.formula}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCopyFormula}
                      className="shrink-0"
                    >
                      {copied ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <Copy className="w-4 h-4" />
                      )}
                    </Button>
                  </div>
                </div>

                {/* Graph Visualization */}
                <GraphRenderer
                  formula={selectedExample.formula}
                  type={selectedExample.type}
                  width={500}
                  height={300}
                  animated={selectedExample.type === '3d'}
                  interactive={true}
                />

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <Button className="flex-1">
                    Try This Example
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                  <Button variant="outline">
                    <Code className="w-4 h-4 mr-2" />
                    Export Code
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* More Examples */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">More Examples</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="all" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="all">All</TabsTrigger>
                    <TabsTrigger value="algebra">Algebra</TabsTrigger>
                    <TabsTrigger value="calculus">Calculus</TabsTrigger>
                    <TabsTrigger value="geometry">Geometry</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="all" className="space-y-3 mt-4">
                    {demoExamples.slice(3).map((example) => (
                      <button
                        key={example.id}
                        onClick={() => handleTryExample(example)}
                        className="w-full text-left p-3 rounded-lg border hover:border-primary/50 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{example.title}</span>
                          <Badge variant="outline" className="text-xs">
                            {example.type.toUpperCase()}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {example.description}
                        </p>
                      </button>
                    ))}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  )
}
