import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        // Mathematical interface variants
        formula: "border-transparent bg-formula-500 text-white hover:bg-formula-600",
        math: "border-transparent bg-math-500 text-white hover:bg-math-600",
        graph: "border-transparent bg-graph-500 text-white hover:bg-graph-600",
        // Status variants
        success: "border-transparent bg-green-500 text-white hover:bg-green-600",
        warning: "border-transparent bg-yellow-500 text-white hover:bg-yellow-600",
        info: "border-transparent bg-blue-500 text-white hover:bg-blue-600",
        // Confidence level variants
        "confidence-high": "border-transparent bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
        "confidence-medium": "border-transparent bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
        "confidence-low": "border-transparent bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      },
      size: {
        default: "px-2.5 py-0.5 text-xs",
        sm: "px-2 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  asChild?: boolean
}

function Badge({ className, variant, size, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />
  )
}

// Specialized badge components for mathematical interface
interface ConfidenceBadgeProps extends Omit<BadgeProps, 'variant'> {
  confidence: number // 0-1 scale
  showPercentage?: boolean
}

function ConfidenceBadge({ 
  confidence, 
  showPercentage = true, 
  className, 
  children,
  ...props 
}: ConfidenceBadgeProps) {
  const getVariant = (conf: number) => {
    if (conf >= 0.8) return 'confidence-high'
    if (conf >= 0.6) return 'confidence-medium'
    return 'confidence-low'
  }

  const percentage = Math.round(confidence * 100)

  return (
    <Badge 
      variant={getVariant(confidence)} 
      className={className}
      {...props}
    >
      {children || (showPercentage ? `${percentage}% confidence` : `${percentage}%`)}
    </Badge>
  )
}

interface StatusBadgeProps extends Omit<BadgeProps, 'variant'> {
  status: 'success' | 'error' | 'warning' | 'info' | 'processing'
}

function StatusBadge({ status, className, children, ...props }: StatusBadgeProps) {
  const variants = {
    success: 'success',
    error: 'destructive',
    warning: 'warning',
    info: 'info',
    processing: 'secondary',
  } as const

  const icons = {
    success: '✓',
    error: '✗',
    warning: '⚠',
    info: 'ℹ',
    processing: '⟳',
  }

  return (
    <Badge 
      variant={variants[status]} 
      className={cn(
        status === 'processing' && 'animate-pulse',
        className
      )}
      {...props}
    >
      <span className="mr-1">{icons[status]}</span>
      {children}
    </Badge>
  )
}

interface VariableBadgeProps extends Omit<BadgeProps, 'variant'> {
  variable: string
  type?: 'input' | 'output' | 'constant'
}

function VariableBadge({ 
  variable, 
  type = 'input', 
  className, 
  ...props 
}: VariableBadgeProps) {
  const typeStyles = {
    input: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    output: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    constant: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  }

  return (
    <Badge 
      variant="outline"
      className={cn(
        'font-mono text-xs',
        typeStyles[type],
        className
      )}
      {...props}
    >
      {variable}
    </Badge>
  )
}

interface OperationBadgeProps extends Omit<BadgeProps, 'variant'> {
  operation: string
  category?: 'arithmetic' | 'calculus' | 'algebra' | 'statistics' | 'geometry'
}

function OperationBadge({ 
  operation, 
  category = 'arithmetic', 
  className, 
  ...props 
}: OperationBadgeProps) {
  const categoryColors = {
    arithmetic: 'formula',
    calculus: 'math',
    algebra: 'graph',
    statistics: 'info',
    geometry: 'secondary',
  } as const

  return (
    <Badge 
      variant={categoryColors[category]}
      className={cn('capitalize', className)}
      {...props}
    >
      {operation}
    </Badge>
  )
}

export { 
  Badge, 
  badgeVariants,
  ConfidenceBadge,
  StatusBadge,
  VariableBadge,
  OperationBadge,
}
