import { NextRequest } from 'next/server'

// Mock the rate limiter
jest.mock('@/lib/rate-limit', () => ({
  rateLimit: () => ({
    check: jest.fn().mockResolvedValue({ success: true })
  })
}))

// Mock Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id', email: '<EMAIL>' } },
        error: null
      })
    },
    from: jest.fn(() => ({
      insert: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({
        data: { id: 'test-id', created_at: new Date().toISOString() },
        error: null
      }),
      eq: jest.fn().mockReturnThis(),
      range: jest.fn().mockReturnThis(),
      order: jest.fn().mockResolvedValue({
        data: [],
        error: null,
        count: 0
      })
    }))
  }))
}))

describe('API Integration Tests', () => {
  describe('/api/math/evaluate', () => {
    it('should evaluate simple mathematical expressions', async () => {
      const { POST } = await import('@/app/api/math/evaluate/route')
      
      const request = new NextRequest('http://localhost:3000/api/math/evaluate', {
        method: 'POST',
        body: JSON.stringify({
          expression: '2 + 3',
          operation: 'evaluate'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.result.result).toBe(5)
    })

    it('should solve equations', async () => {
      const { POST } = await import('@/app/api/math/evaluate/route')
      
      const request = new NextRequest('http://localhost:3000/api/math/evaluate', {
        method: 'POST',
        body: JSON.stringify({
          expression: '2*x + 5 = 15',
          operation: 'solve',
          variable: 'x'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.result.variable).toBe('x')
      expect(data.result.solutions).toBeDefined()
    })

    it('should calculate derivatives', async () => {
      const { POST } = await import('@/app/api/math/evaluate/route')
      
      const request = new NextRequest('http://localhost:3000/api/math/evaluate', {
        method: 'POST',
        body: JSON.stringify({
          expression: 'x^2',
          operation: 'derivative',
          variable: 'x'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.result.derivative).toBeDefined()
      expect(data.result.steps).toBeDefined()
    })

    it('should handle matrix operations', async () => {
      const { POST } = await import('@/app/api/math/evaluate/route')
      
      const request = new NextRequest('http://localhost:3000/api/math/evaluate', {
        method: 'POST',
        body: JSON.stringify({
          operation: 'matrix',
          matrices: [[[1, 2], [3, 4]], [[5, 6], [7, 8]]],
          matrixOperation: 'add'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.result.result).toEqual([[6, 8], [10, 12]])
    })

    it('should return 400 for invalid expressions', async () => {
      const { POST } = await import('@/app/api/math/evaluate/route')
      
      const request = new NextRequest('http://localhost:3000/api/math/evaluate', {
        method: 'POST',
        body: JSON.stringify({
          expression: '',
          operation: 'evaluate'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBeDefined()
    })

    it('should sanitize malicious input', async () => {
      const { POST } = await import('@/app/api/math/evaluate/route')
      
      const request = new NextRequest('http://localhost:3000/api/math/evaluate', {
        method: 'POST',
        body: JSON.stringify({
          expression: 'eval("malicious code")',
          operation: 'evaluate'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('invalid characters')
    })
  })

  describe('/api/nlp/process', () => {
    it('should process natural language input', async () => {
      const { POST } = await import('@/app/api/nlp/process/route')
      
      const request = new NextRequest('http://localhost:3000/api/nlp/process', {
        method: 'POST',
        body: JSON.stringify({
          input: 'solve x plus 5 equals 10',
          useAI: false
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.nlp.intent).toBe('solve')
      expect(data.nlp.formula).toBeDefined()
      expect(data.nlp.confidence).toBeGreaterThan(0)
    })

    it('should return suggestions for improvement', async () => {
      const { POST } = await import('@/app/api/nlp/process/route')
      
      const request = new NextRequest('http://localhost:3000/api/nlp/process', {
        method: 'POST',
        body: JSON.stringify({
          input: 'do math',
          useAI: false
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.suggestions).toBeDefined()
      expect(Array.isArray(data.suggestions)).toBe(true)
    })

    it('should handle empty input', async () => {
      const { POST } = await import('@/app/api/nlp/process/route')
      
      const request = new NextRequest('http://localhost:3000/api/nlp/process', {
        method: 'POST',
        body: JSON.stringify({
          input: '',
          useAI: false
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBeDefined()
    })

    it('should reject overly long input', async () => {
      const { POST } = await import('@/app/api/nlp/process/route')
      
      const longInput = 'a'.repeat(1001)
      const request = new NextRequest('http://localhost:3000/api/nlp/process', {
        method: 'POST',
        body: JSON.stringify({
          input: longInput,
          useAI: false
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('too long')
    })
  })

  describe('/api/export/code', () => {
    it('should export Python code', async () => {
      const { POST } = await import('@/app/api/export/code/route')
      
      const request = new NextRequest('http://localhost:3000/api/export/code', {
        method: 'POST',
        body: JSON.stringify({
          formula: 'x^2 + 2*x + 1',
          language: 'python',
          functionName: 'quadratic',
          variables: { x: 1 },
          includeComments: true,
          includeTests: true
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.code).toContain('def quadratic')
      expect(data.code).toContain('import math')
      expect(data.filename).toBe('quadratic.py')
      expect(data.language).toBe('python')
    })

    it('should export JavaScript code', async () => {
      const { POST } = await import('@/app/api/export/code/route')
      
      const request = new NextRequest('http://localhost:3000/api/export/code', {
        method: 'POST',
        body: JSON.stringify({
          formula: 'Math.sin(x)',
          language: 'javascript',
          functionName: 'sineFunction',
          variables: { x: 0 }
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.code).toContain('function sineFunction')
      expect(data.code).toContain('Math.sin')
      expect(data.filename).toBe('sineFunction.js')
    })

    it('should reject unsupported languages', async () => {
      const { POST } = await import('@/app/api/export/code/route')
      
      const request = new NextRequest('http://localhost:3000/api/export/code', {
        method: 'POST',
        body: JSON.stringify({
          formula: 'x + 1',
          language: 'unsupported',
          functionName: 'test'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('Unsupported language')
    })

    it('should require formula and language', async () => {
      const { POST } = await import('@/app/api/export/code/route')
      
      const request = new NextRequest('http://localhost:3000/api/export/code', {
        method: 'POST',
        body: JSON.stringify({
          functionName: 'test'
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('Formula and language are required')
    })
  })

  describe('Health Check Endpoints', () => {
    it('should return health status for math API', async () => {
      const { GET } = await import('@/app/api/math/evaluate/route')
      
      const request = new NextRequest('http://localhost:3000/api/math/evaluate', {
        method: 'GET'
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.status).toBe('healthy')
      expect(data.service).toBe('mathematical-engine')
    })

    it('should return health status for NLP API', async () => {
      const { GET } = await import('@/app/api/nlp/process/route')
      
      const request = new NextRequest('http://localhost:3000/api/nlp/process', {
        method: 'GET'
      })

      const response = await GET(request)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.status).toBe('healthy')
      expect(data.service).toBe('nlp-processor')
    })
  })

  describe('CORS Support', () => {
    it('should handle OPTIONS requests', async () => {
      const { OPTIONS } = await import('@/app/api/math/evaluate/route')
      
      const request = new NextRequest('http://localhost:3000/api/math/evaluate', {
        method: 'OPTIONS'
      })

      const response = await OPTIONS(request)

      expect(response.status).toBe(200)
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*')
      expect(response.headers.get('Access-Control-Allow-Methods')).toContain('POST')
    })
  })

  describe('Error Handling', () => {
    it('should handle malformed JSON', async () => {
      const { POST } = await import('@/app/api/math/evaluate/route')
      
      const request = new NextRequest('http://localhost:3000/api/math/evaluate', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const response = await POST(request)

      expect(response.status).toBe(500)
    })

    it('should handle missing content-type', async () => {
      const { POST } = await import('@/app/api/math/evaluate/route')
      
      const request = new NextRequest('http://localhost:3000/api/math/evaluate', {
        method: 'POST',
        body: JSON.stringify({ expression: '1+1' })
      })

      const response = await POST(request)
      const data = await response.json()

      // Should still work as Next.js handles this gracefully
      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
    })
  })
})
