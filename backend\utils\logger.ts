/**
 * Production-ready logging system for FormulaForge
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  context?: string
  metadata?: Record<string, any>
  error?: Error
  requestId?: string
  userId?: string
  ip?: string
}

export class Logger {
  private static instance: Logger
  private logLevel: LogLevel
  private context: string

  constructor(context: string = 'App', logLevel: LogLevel = LogLevel.INFO) {
    this.context = context
    this.logLevel = process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : logLevel
  }

  static getInstance(context?: string): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(context)
    }
    return Logger.instance
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.logLevel
  }

  private formatLogEntry(entry: LogEntry): string {
    const timestamp = entry.timestamp
    const level = LogLevel[entry.level]
    const context = entry.context || this.context
    const message = entry.message

    let formatted = `[${timestamp}] ${level} [${context}] ${message}`

    if (entry.requestId) {
      formatted += ` [RequestID: ${entry.requestId}]`
    }

    if (entry.userId) {
      formatted += ` [UserID: ${entry.userId}]`
    }

    if (entry.ip) {
      formatted += ` [IP: ${entry.ip}]`
    }

    if (entry.metadata && Object.keys(entry.metadata).length > 0) {
      formatted += ` [Metadata: ${JSON.stringify(entry.metadata)}]`
    }

    if (entry.error) {
      formatted += `\n  Error: ${entry.error.message}`
      if (entry.error.stack) {
        formatted += `\n  Stack: ${entry.error.stack}`
      }
    }

    return formatted
  }

  private writeLog(entry: LogEntry): void {
    const formatted = this.formatLogEntry(entry)

    // In production, you might want to send logs to external services
    // like CloudWatch, Datadog, or Sentry
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to external logging service
      this.sendToExternalService(entry)
    }

    // Console output with appropriate method
    switch (entry.level) {
      case LogLevel.ERROR:
        console.error(formatted)
        break
      case LogLevel.WARN:
        console.warn(formatted)
        break
      case LogLevel.INFO:
        console.info(formatted)
        break
      case LogLevel.DEBUG:
        console.debug(formatted)
        break
    }
  }

  private sendToExternalService(entry: LogEntry): void {
    // Placeholder for external logging service integration
    // Examples: Sentry, DataDog, CloudWatch, etc.
    
    if (process.env.SENTRY_DSN && entry.level === LogLevel.ERROR) {
      // Example Sentry integration
      // Sentry.captureException(entry.error || new Error(entry.message))
    }

    if (process.env.DATADOG_API_KEY) {
      // Example DataDog integration
      // Send structured log to DataDog
    }
  }

  error(message: string, error?: Error, metadata?: Record<string, any>, requestId?: string, userId?: string, ip?: string): void {
    if (!this.shouldLog(LogLevel.ERROR)) return

    this.writeLog({
      timestamp: new Date().toISOString(),
      level: LogLevel.ERROR,
      message,
      context: this.context,
      error,
      metadata,
      requestId,
      userId,
      ip
    })
  }

  warn(message: string, metadata?: Record<string, any>, requestId?: string, userId?: string): void {
    if (!this.shouldLog(LogLevel.WARN)) return

    this.writeLog({
      timestamp: new Date().toISOString(),
      level: LogLevel.WARN,
      message,
      context: this.context,
      metadata,
      requestId,
      userId
    })
  }

  info(message: string, metadata?: Record<string, any>, requestId?: string, userId?: string): void {
    if (!this.shouldLog(LogLevel.INFO)) return

    this.writeLog({
      timestamp: new Date().toISOString(),
      level: LogLevel.INFO,
      message,
      context: this.context,
      metadata,
      requestId,
      userId
    })
  }

  debug(message: string, metadata?: Record<string, any>, requestId?: string): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return

    this.writeLog({
      timestamp: new Date().toISOString(),
      level: LogLevel.DEBUG,
      message,
      context: this.context,
      metadata,
      requestId
    })
  }

  // Convenience methods for common scenarios
  apiRequest(method: string, path: string, statusCode: number, responseTime: number, requestId: string, userId?: string, ip?: string): void {
    this.info(`API Request: ${method} ${path}`, {
      statusCode,
      responseTime,
      method,
      path
    }, requestId, userId)
  }

  apiError(method: string, path: string, error: Error, requestId: string, userId?: string, ip?: string): void {
    this.error(`API Error: ${method} ${path}`, error, {
      method,
      path
    }, requestId, userId, ip)
  }

  securityEvent(event: string, details: Record<string, any>, ip?: string, userId?: string): void {
    this.warn(`Security Event: ${event}`, {
      ...details,
      securityEvent: true
    }, undefined, userId)
  }

  performanceWarning(operation: string, duration: number, threshold: number, metadata?: Record<string, any>): void {
    this.warn(`Performance Warning: ${operation} took ${duration}ms (threshold: ${threshold}ms)`, {
      operation,
      duration,
      threshold,
      ...metadata
    })
  }

  userAction(action: string, userId: string, metadata?: Record<string, any>, requestId?: string): void {
    this.info(`User Action: ${action}`, {
      action,
      ...metadata
    }, requestId, userId)
  }

  // Method to create child logger with additional context
  child(additionalContext: string): Logger {
    return new Logger(`${this.context}:${additionalContext}`, this.logLevel)
  }
}

// Export singleton instances for common use cases
export const logger = Logger.getInstance('FormulaForge')
export const apiLogger = Logger.getInstance('API')
export const authLogger = Logger.getInstance('Auth')
export const collaborationLogger = Logger.getInstance('Collaboration')
export const formulaLogger = Logger.getInstance('Formula')

// Error classes with logging integration
export class LoggedError extends Error {
  constructor(
    message: string,
    public code: string = 'UNKNOWN_ERROR',
    public statusCode: number = 500,
    public metadata?: Record<string, any>,
    public requestId?: string,
    public userId?: string
  ) {
    super(message)
    this.name = 'LoggedError'
    
    // Automatically log the error
    logger.error(message, this, {
      code,
      statusCode,
      ...metadata
    }, requestId, userId)
  }
}

export class ValidationError extends LoggedError {
  constructor(message: string, field?: string, requestId?: string) {
    super(message, 'VALIDATION_ERROR', 400, { field }, requestId)
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends LoggedError {
  constructor(message: string = 'Authentication failed', requestId?: string) {
    super(message, 'AUTH_ERROR', 401, {}, requestId)
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends LoggedError {
  constructor(message: string = 'Access denied', userId?: string, requestId?: string) {
    super(message, 'AUTHORIZATION_ERROR', 403, {}, requestId, userId)
    this.name = 'AuthorizationError'
  }
}

export class RateLimitError extends LoggedError {
  constructor(message: string = 'Rate limit exceeded', ip?: string, requestId?: string) {
    super(message, 'RATE_LIMIT_ERROR', 429, { ip }, requestId)
    this.name = 'RateLimitError'
  }
}

export class FormulaError extends LoggedError {
  constructor(message: string, formula?: string, requestId?: string, userId?: string) {
    super(message, 'FORMULA_ERROR', 400, { formula }, requestId, userId)
    this.name = 'FormulaError'
  }
}

// Utility function to extract request context
export function getRequestContext(request: any): {
  requestId?: string
  userId?: string
  ip?: string
} {
  return {
    requestId: request.headers?.get?.('x-request-id') || request.requestId,
    userId: request.user?.id || request.userId,
    ip: request.headers?.get?.('x-forwarded-for')?.split(',')[0] || 
        request.headers?.get?.('x-real-ip') || 
        request.ip
  }
}

// Performance monitoring decorator
export function logPerformance(threshold: number = 1000) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const start = Date.now()
      const methodLogger = logger.child(`${target.constructor.name}.${propertyName}`)
      
      try {
        const result = await method.apply(this, args)
        const duration = Date.now() - start
        
        if (duration > threshold) {
          methodLogger.performanceWarning(propertyName, duration, threshold)
        } else {
          methodLogger.debug(`Method ${propertyName} completed in ${duration}ms`)
        }
        
        return result
      } catch (error) {
        const duration = Date.now() - start
        methodLogger.error(`Method ${propertyName} failed after ${duration}ms`, error as Error)
        throw error
      }
    }

    return descriptor
  }
}
