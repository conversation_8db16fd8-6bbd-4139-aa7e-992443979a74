'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  Users, 
  Share2, 
  MessageCircle, 
  Eye, 
  Edit3,
  Save,
  History,
  UserPlus,
  Settings
} from 'lucide-react'

interface CollaboratorCursor {
  id: string
  user: string
  position: number
  color: string
}

interface CollaboratorPresence {
  id: string
  name: string
  email: string
  avatar?: string
  color: string
  isActive: boolean
  lastSeen: Date
}

interface RealTimeEditorProps {
  documentId?: string
  initialContent?: string
  readOnly?: boolean
  onContentChange?: (content: string) => void
  onCollaboratorJoin?: (collaborator: CollaboratorPresence) => void
  onCollaboratorLeave?: (collaboratorId: string) => void
}

export function RealTimeEditor({
  documentId = 'default',
  initialContent = '',
  readOnly = false,
  onContentChange,
  onCollaboratorJoin,
  onCollaboratorLeave
}: RealTimeEditorProps) {
  const [content, setContent] = useState(initialContent)
  const [collaborators, setCollaborators] = useState<CollaboratorPresence[]>([])
  const [cursors, setCursors] = useState<CollaboratorCursor[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [showChat, setShowChat] = useState(false)
  const [chatMessages, setChatMessages] = useState<Array<{
    id: string
    user: string
    message: string
    timestamp: Date
  }>>([])
  const [newMessage, setNewMessage] = useState('')
  
  const editorRef = useRef<HTMLTextAreaElement>(null)
  const wsRef = useRef<WebSocket | null>(null)

  // Initialize WebSocket connection for real-time collaboration
  useEffect(() => {
    if (typeof window === 'undefined') return

    const ws = new WebSocket(`ws://localhost:3001/collaboration/${documentId}`)
    wsRef.current = ws

    ws.onopen = () => {
      setIsConnected(true)
      console.log('Connected to collaboration server')
    }

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      
      switch (data.type) {
        case 'content-change':
          setContent(data.content)
          onContentChange?.(data.content)
          break
        case 'cursor-update':
          setCursors(prev => {
            const filtered = prev.filter(c => c.id !== data.cursor.id)
            return [...filtered, data.cursor]
          })
          break
        case 'collaborator-join':
          setCollaborators(prev => [...prev, data.collaborator])
          onCollaboratorJoin?.(data.collaborator)
          break
        case 'collaborator-leave':
          setCollaborators(prev => prev.filter(c => c.id !== data.collaboratorId))
          setCursors(prev => prev.filter(c => c.id !== data.collaboratorId))
          onCollaboratorLeave?.(data.collaboratorId)
          break
        case 'chat-message':
          setChatMessages(prev => [...prev, data.message])
          break
      }
    }

    ws.onclose = () => {
      setIsConnected(false)
      console.log('Disconnected from collaboration server')
    }

    return () => {
      ws.close()
    }
  }, [documentId, onContentChange, onCollaboratorJoin, onCollaboratorLeave])

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
    
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'content-change',
        content: newContent,
        documentId
      }))
    }
    
    onContentChange?.(newContent)
  }

  const handleCursorMove = (position: number) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: 'cursor-update',
        cursor: {
          id: 'current-user', // This would be the actual user ID
          user: 'Current User',
          position,
          color: '#3b82f6'
        },
        documentId
      }))
    }
  }

  const sendChatMessage = () => {
    if (!newMessage.trim() || !wsRef.current) return

    const message = {
      id: Date.now().toString(),
      user: 'Current User',
      message: newMessage,
      timestamp: new Date()
    }

    wsRef.current.send(JSON.stringify({
      type: 'chat-message',
      message,
      documentId
    }))

    setNewMessage('')
  }

  const inviteCollaborator = () => {
    // This would open an invite dialog
    console.log('Invite collaborator functionality')
  }

  return (
    <div className="flex h-full">
      {/* Main Editor */}
      <div className="flex-1 flex flex-col">
        <Card className="flex-1 flex flex-col">
          <CardHeader className="flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-lg">Collaborative Editor</CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant={isConnected ? "default" : "destructive"}>
                {isConnected ? "Connected" : "Disconnected"}
              </Badge>
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                <span className="text-sm">{collaborators.length}</span>
              </div>
              <Button size="sm" variant="outline" onClick={inviteCollaborator}>
                <UserPlus className="w-4 h-4 mr-1" />
                Invite
              </Button>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => setShowChat(!showChat)}
              >
                <MessageCircle className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col">
            {/* Collaborator Avatars */}
            {collaborators.length > 0 && (
              <div className="flex items-center gap-2 mb-4 p-2 bg-muted/30 rounded-lg">
                {collaborators.map((collaborator) => (
                  <div key={collaborator.id} className="flex items-center gap-2">
                    <div 
                      className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                      style={{ backgroundColor: collaborator.color }}
                    >
                      {collaborator.name.charAt(0).toUpperCase()}
                    </div>
                    <span className="text-sm">{collaborator.name}</span>
                    {collaborator.isActive && (
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Editor */}
            <div className="flex-1 relative">
              <Textarea
                ref={editorRef}
                value={content}
                onChange={(e) => handleContentChange(e.target.value)}
                onSelect={(e) => {
                  const target = e.target as HTMLTextAreaElement
                  handleCursorMove(target.selectionStart)
                }}
                placeholder="Start typing your mathematical formulas and equations..."
                className="w-full h-full resize-none font-mono text-sm"
                readOnly={readOnly}
              />
              
              {/* Collaborative Cursors */}
              {cursors.map((cursor) => (
                <div
                  key={cursor.id}
                  className="absolute pointer-events-none z-10"
                  style={{
                    left: `${cursor.position * 8}px`, // Approximate character width
                    top: '10px',
                    borderLeft: `2px solid ${cursor.color}`
                  }}
                >
                  <div 
                    className="absolute -top-6 left-0 px-2 py-1 text-xs text-white rounded"
                    style={{ backgroundColor: cursor.color }}
                  >
                    {cursor.user}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Chat Sidebar */}
      {showChat && (
        <div className="w-80 border-l">
          <Card className="h-full rounded-l-none">
            <CardHeader>
              <CardTitle className="text-lg">Chat</CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col h-full">
              <div className="flex-1 overflow-y-auto space-y-2 mb-4">
                {chatMessages.map((msg) => (
                  <div key={msg.id} className="p-2 bg-muted/30 rounded">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium text-sm">{msg.user}</span>
                      <span className="text-xs text-muted-foreground">
                        {msg.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="text-sm">{msg.message}</p>
                  </div>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type a message..."
                  onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
                />
                <Button size="sm" onClick={sendChatMessage}>
                  Send
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
