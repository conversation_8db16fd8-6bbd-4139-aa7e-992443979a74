import { NextRequest, NextResponse } from 'next/server'

// Rate limiting storage (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

// Security headers configuration
const securityHeaders = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'X-XSS-Protection': '1; mode=block',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https:",
    "connect-src 'self' ws: wss:",
    "frame-ancestors 'none'"
  ].join('; ')
}

// Rate limiting configuration by endpoint
const rateLimitConfig = {
  '/api/formulas/generate': { requests: 10, window: 60000 }, // 10 requests per minute
  '/api/formulas/evaluate': { requests: 20, window: 60000 }, // 20 requests per minute
  '/api/collaboration/sessions': { requests: 30, window: 60000 }, // 30 requests per minute
  '/api/auth/session': { requests: 5, window: 300000 }, // 5 requests per 5 minutes
  default: { requests: 100, window: 60000 } // Default: 100 requests per minute
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const ip = forwarded ? forwarded.split(',')[0] : realIP || 'unknown'
  return ip.trim()
}

function getRateLimitKey(request: NextRequest): string {
  const ip = getClientIP(request)
  const pathname = request.nextUrl.pathname
  return `${ip}:${pathname}`
}

function checkRateLimit(request: NextRequest): { allowed: boolean; remaining: number; resetTime: number } {
  const key = getRateLimitKey(request)
  const pathname = request.nextUrl.pathname
  
  // Find matching rate limit config
  let config = rateLimitConfig.default
  for (const [path, pathConfig] of Object.entries(rateLimitConfig)) {
    if (path !== 'default' && pathname.startsWith(path)) {
      config = pathConfig
      break
    }
  }

  const now = Date.now()
  const record = rateLimitStore.get(key)

  if (!record || now > record.resetTime) {
    const newRecord = { count: 1, resetTime: now + config.window }
    rateLimitStore.set(key, newRecord)
    return { 
      allowed: true, 
      remaining: config.requests - 1, 
      resetTime: newRecord.resetTime 
    }
  }

  if (record.count >= config.requests) {
    return { 
      allowed: false, 
      remaining: 0, 
      resetTime: record.resetTime 
    }
  }

  record.count++
  return { 
    allowed: true, 
    remaining: config.requests - record.count, 
    resetTime: record.resetTime 
  }
}

function isBot(userAgent: string): boolean {
  const botPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i,
    /go-http-client/i,
    /okhttp/i
  ]
  
  return botPatterns.some(pattern => pattern.test(userAgent))
}

function validateRequest(request: NextRequest): { valid: boolean; reason?: string } {
  const userAgent = request.headers.get('user-agent') || ''
  const contentType = request.headers.get('content-type') || ''
  const origin = request.headers.get('origin')
  const referer = request.headers.get('referer')

  // Block requests with suspicious user agents
  if (userAgent.length === 0) {
    return { valid: false, reason: 'Missing user agent' }
  }

  // Block obvious bots for API endpoints (except for legitimate monitoring)
  if (request.nextUrl.pathname.startsWith('/api/') && isBot(userAgent)) {
    const allowedBots = ['googlebot', 'bingbot', 'uptimerobot']
    const isAllowedBot = allowedBots.some(bot => userAgent.toLowerCase().includes(bot))
    if (!isAllowedBot) {
      return { valid: false, reason: 'Bot access not allowed' }
    }
  }

  // Validate content type for POST/PUT requests
  if (['POST', 'PUT', 'PATCH'].includes(request.method)) {
    if (!contentType.includes('application/json') && !contentType.includes('multipart/form-data')) {
      return { valid: false, reason: 'Invalid content type' }
    }
  }

  // CORS validation for API requests
  if (request.nextUrl.pathname.startsWith('/api/') && origin) {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'http://localhost:3003',
      'http://localhost:3004',
      'https://formulaforge.com',
      'https://www.formulaforge.com'
    ]
    
    if (process.env.NODE_ENV === 'development') {
      // Allow any localhost origin in development
      if (!origin.startsWith('http://localhost:') && !allowedOrigins.includes(origin)) {
        return { valid: false, reason: 'CORS: Origin not allowed' }
      }
    } else {
      if (!allowedOrigins.includes(origin)) {
        return { valid: false, reason: 'CORS: Origin not allowed' }
      }
    }
  }

  return { valid: true }
}

export function middleware(request: NextRequest) {
  const response = NextResponse.next()
  const pathname = request.nextUrl.pathname

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.startsWith('/public/') ||
    pathname.includes('.')
  ) {
    return response
  }

  // Add security headers
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })

  // Add CORS headers for API routes
  if (pathname.startsWith('/api/')) {
    const origin = request.headers.get('origin')
    
    if (origin) {
      response.headers.set('Access-Control-Allow-Origin', origin)
    }
    
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
    response.headers.set('Access-Control-Allow-Credentials', 'true')
    response.headers.set('Access-Control-Max-Age', '86400')

    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new NextResponse(null, { status: 200, headers: response.headers })
    }
  }

  // Validate request
  const validation = validateRequest(request)
  if (!validation.valid) {
    console.warn(`Blocked request: ${validation.reason} - ${getClientIP(request)} - ${request.nextUrl.pathname}`)
    return new NextResponse(
      JSON.stringify({ error: 'Request blocked', reason: validation.reason }),
      { 
        status: 403, 
        headers: { 
          'Content-Type': 'application/json',
          ...Object.fromEntries(response.headers.entries())
        }
      }
    )
  }

  // Apply rate limiting to API routes
  if (pathname.startsWith('/api/')) {
    const rateLimit = checkRateLimit(request)
    
    // Add rate limit headers
    response.headers.set('X-RateLimit-Remaining', rateLimit.remaining.toString())
    response.headers.set('X-RateLimit-Reset', Math.ceil(rateLimit.resetTime / 1000).toString())
    
    if (!rateLimit.allowed) {
      console.warn(`Rate limit exceeded: ${getClientIP(request)} - ${pathname}`)
      return new NextResponse(
        JSON.stringify({ 
          error: 'Rate limit exceeded', 
          retryAfter: Math.ceil((rateLimit.resetTime - Date.now()) / 1000)
        }),
        { 
          status: 429, 
          headers: { 
            'Content-Type': 'application/json',
            'Retry-After': Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString(),
            ...Object.fromEntries(response.headers.entries())
          }
        }
      )
    }
  }

  // Add request ID for tracing
  const requestId = Math.random().toString(36).substring(2) + Date.now().toString(36)
  response.headers.set('X-Request-ID', requestId)

  // Log API requests (in production, use proper logging)
  if (pathname.startsWith('/api/')) {
    console.log(`API Request: ${request.method} ${pathname} - ${getClientIP(request)} - ${requestId}`)
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
}
