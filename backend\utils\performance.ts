/**
 * Performance monitoring and optimization utilities
 */

import { logger } from './logger'

export interface PerformanceMetrics {
  operation: string
  duration: number
  timestamp: string
  metadata?: Record<string, any>
  memoryUsage?: NodeJS.MemoryUsage
  cpuUsage?: NodeJS.CpuUsage
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: PerformanceMetrics[] = []
  private maxMetrics: number = 1000
  private thresholds: Record<string, number> = {
    'api.request': 1000,
    'formula.generation': 2000,
    'formula.evaluation': 500,
    'database.query': 100,
    'file.upload': 5000
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  setThreshold(operation: string, threshold: number): void {
    this.thresholds[operation] = threshold
  }

  startTimer(operation: string): PerformanceTimer {
    return new PerformanceTimer(operation, this)
  }

  recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric)
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }

    // Check against thresholds
    const threshold = this.thresholds[metric.operation] || this.thresholds['default'] || 1000
    if (metric.duration > threshold) {
      logger.performanceWarning(metric.operation, metric.duration, threshold, metric.metadata)
    }

    // Log extreme performance issues
    if (metric.duration > threshold * 3) {
      logger.error(`Severe performance issue: ${metric.operation} took ${metric.duration}ms`, undefined, {
        ...metric.metadata,
        severity: 'critical'
      })
    }
  }

  getMetrics(operation?: string, limit: number = 100): PerformanceMetrics[] {
    let filtered = this.metrics
    
    if (operation) {
      filtered = this.metrics.filter(m => m.operation === operation)
    }
    
    return filtered.slice(-limit)
  }

  getAveragePerformance(operation: string, timeWindow: number = 300000): {
    average: number
    count: number
    min: number
    max: number
  } {
    const cutoff = Date.now() - timeWindow
    const relevantMetrics = this.metrics.filter(m => 
      m.operation === operation && 
      new Date(m.timestamp).getTime() > cutoff
    )

    if (relevantMetrics.length === 0) {
      return { average: 0, count: 0, min: 0, max: 0 }
    }

    const durations = relevantMetrics.map(m => m.duration)
    const sum = durations.reduce((a, b) => a + b, 0)
    
    return {
      average: sum / durations.length,
      count: durations.length,
      min: Math.min(...durations),
      max: Math.max(...durations)
    }
  }

  clearMetrics(): void {
    this.metrics = []
  }

  // Get system performance info
  getSystemMetrics(): {
    memory: NodeJS.MemoryUsage
    uptime: number
    loadAverage?: number[]
  } {
    return {
      memory: process.memoryUsage(),
      uptime: process.uptime(),
      loadAverage: process.platform !== 'win32' ? require('os').loadavg() : undefined
    }
  }
}

export class PerformanceTimer {
  private startTime: number
  private startCpuUsage?: NodeJS.CpuUsage
  private operation: string
  private monitor: PerformanceMonitor
  private metadata: Record<string, any> = {}

  constructor(operation: string, monitor: PerformanceMonitor) {
    this.operation = operation
    this.monitor = monitor
    this.startTime = Date.now()
    
    // Capture CPU usage if available
    if (process.cpuUsage) {
      this.startCpuUsage = process.cpuUsage()
    }
  }

  addMetadata(key: string, value: any): PerformanceTimer {
    this.metadata[key] = value
    return this
  }

  end(): number {
    const duration = Date.now() - this.startTime
    const endCpuUsage = this.startCpuUsage ? process.cpuUsage(this.startCpuUsage) : undefined
    
    const metric: PerformanceMetrics = {
      operation: this.operation,
      duration,
      timestamp: new Date().toISOString(),
      metadata: this.metadata,
      memoryUsage: process.memoryUsage(),
      cpuUsage: endCpuUsage
    }

    this.monitor.recordMetric(metric)
    return duration
  }
}

// Singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance()

// Decorator for automatic performance monitoring
export function monitor(operation?: string, threshold?: number) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    const operationName = operation || `${target.constructor.name}.${propertyName}`

    if (threshold) {
      performanceMonitor.setThreshold(operationName, threshold)
    }

    descriptor.value = async function (...args: any[]) {
      const timer = performanceMonitor.startTimer(operationName)
      
      // Add context metadata
      timer.addMetadata('args_count', args.length)
      timer.addMetadata('method', propertyName)
      timer.addMetadata('class', target.constructor.name)

      try {
        const result = await method.apply(this, args)
        timer.addMetadata('success', true)
        timer.end()
        return result
      } catch (error) {
        timer.addMetadata('success', false)
        timer.addMetadata('error', (error as Error).message)
        timer.end()
        throw error
      }
    }

    return descriptor
  }
}

// Utility functions for common performance patterns
export async function measureAsync<T>(
  operation: string, 
  fn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  const timer = performanceMonitor.startTimer(operation)
  
  if (metadata) {
    Object.entries(metadata).forEach(([key, value]) => {
      timer.addMetadata(key, value)
    })
  }

  try {
    const result = await fn()
    timer.addMetadata('success', true)
    timer.end()
    return result
  } catch (error) {
    timer.addMetadata('success', false)
    timer.addMetadata('error', (error as Error).message)
    timer.end()
    throw error
  }
}

export function measureSync<T>(
  operation: string, 
  fn: () => T,
  metadata?: Record<string, any>
): T {
  const timer = performanceMonitor.startTimer(operation)
  
  if (metadata) {
    Object.entries(metadata).forEach(([key, value]) => {
      timer.addMetadata(key, value)
    })
  }

  try {
    const result = fn()
    timer.addMetadata('success', true)
    timer.end()
    return result
  } catch (error) {
    timer.addMetadata('success', false)
    timer.addMetadata('error', (error as Error).message)
    timer.end()
    throw error
  }
}

// Memory usage monitoring
export class MemoryMonitor {
  private static instance: MemoryMonitor
  private warningThreshold: number = 100 * 1024 * 1024 // 100MB
  private criticalThreshold: number = 500 * 1024 * 1024 // 500MB
  private checkInterval: NodeJS.Timeout | null = null

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor()
    }
    return MemoryMonitor.instance
  }

  startMonitoring(intervalMs: number = 30000): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }

    this.checkInterval = setInterval(() => {
      this.checkMemoryUsage()
    }, intervalMs)
  }

  stopMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }

  private checkMemoryUsage(): void {
    const usage = process.memoryUsage()
    const heapUsed = usage.heapUsed

    if (heapUsed > this.criticalThreshold) {
      logger.error('Critical memory usage detected', undefined, {
        heapUsed: Math.round(heapUsed / 1024 / 1024),
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
        external: Math.round(usage.external / 1024 / 1024),
        rss: Math.round(usage.rss / 1024 / 1024)
      })
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
        logger.info('Forced garbage collection due to high memory usage')
      }
    } else if (heapUsed > this.warningThreshold) {
      logger.warn('High memory usage detected', {
        heapUsed: Math.round(heapUsed / 1024 / 1024),
        heapTotal: Math.round(usage.heapTotal / 1024 / 1024)
      })
    }
  }

  setThresholds(warning: number, critical: number): void {
    this.warningThreshold = warning
    this.criticalThreshold = critical
  }
}

// Export singleton
export const memoryMonitor = MemoryMonitor.getInstance()

// Initialize monitoring in production
if (process.env.NODE_ENV === 'production') {
  memoryMonitor.startMonitoring()
  
  // Set performance thresholds for production
  performanceMonitor.setThreshold('api.request', 500)
  performanceMonitor.setThreshold('formula.generation', 1000)
  performanceMonitor.setThreshold('formula.evaluation', 200)
  performanceMonitor.setThreshold('database.query', 50)
}
