{
  "compilerOptions": {
    // Language and Environment
    "target": "ES2022",
    "lib": [
      "dom",
      "dom.iterable",
      "es6",
      "es2022",
      "webworker"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    
    // Type Checking
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "exactOptionalPropertyTypes": false,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": false,
    "noUncheckedIndexedAccess": false,
    
    // Emit
    "declaration": false,
    "declarationMap": false,
    "sourceMap": true,
    "removeComments": false,
    "importHelpers": true,
    
    // Interop Constraints
    "allowSyntheticDefaultImports": true,
    "verbatimModuleSyntax": false,
    
    // Path Mapping
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@/components/*": ["./components/*"],
      "@/lib/*": ["./lib/*"],
      "@/hooks/*": ["./hooks/*"],
      "@/utils/*": ["./utils/*"],
      "@/types/*": ["./types/*"],
      "@/styles/*": ["./styles/*"],
      "@/backend/*": ["./backend/*"],
      "@/engines/*": ["./backend/engines/*"],
      "@/ai/*": ["./backend/ai/*"],
      "@/nlp/*": ["./backend/nlp/*"]
    },
    
    // Plugin Configuration
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    "backend/**/*.ts",
    "components/**/*.ts",
    "components/**/*.tsx",
    "lib/**/*.ts",
    "hooks/**/*.ts",
    "utils/**/*.ts",
    "types/**/*.ts",
    "app/**/*.ts",
    "app/**/*.tsx",
    "pages/**/*.ts",
    "pages/**/*.tsx"
  ],
  "exclude": [
    "node_modules",
    ".next",
    "out",
    "dist",
    "build",
    "coverage",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx"
  ],
  "ts-node": {
    "compilerOptions": {
      "module": "CommonJS"
    }
  }
}
