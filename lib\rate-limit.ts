import { NextRequest } from 'next/server'

interface RateLimitOptions {
  interval: number // Time window in milliseconds
  uniqueTokenPerInterval: number // Maximum number of unique tokens per interval
}

interface RateLimitResult {
  success: boolean
  limit: number
  remaining: number
  reset: number
}

// In-memory store for rate limiting (use Redis in production)
const tokenCache = new Map<string, { count: number; reset: number }>()

/**
 * Simple in-memory rate limiter
 * In production, use Redis or a dedicated rate limiting service
 */
export function rateLimit(options: RateLimitOptions) {
  const { interval, uniqueTokenPerInterval } = options

  return {
    async check(
      request: NextRequest,
      limit: number,
      token?: string
    ): Promise<RateLimitResult> {
      // Generate token from IP address or provided token
      const identifier = token || getClientIdentifier(request)
      const now = Date.now()
      const windowStart = now - interval

      // Clean up expired entries
      for (const [key, value] of tokenCache.entries()) {
        if (value.reset < now) {
          tokenCache.delete(key)
        }
      }

      // Get or create rate limit entry
      let entry = tokenCache.get(identifier)
      
      if (!entry || entry.reset < now) {
        // Create new entry or reset expired entry
        entry = {
          count: 0,
          reset: now + interval,
        }
      }

      // Check if limit exceeded
      if (entry.count >= limit) {
        return {
          success: false,
          limit,
          remaining: 0,
          reset: entry.reset,
        }
      }

      // Increment counter
      entry.count++
      tokenCache.set(identifier, entry)

      return {
        success: true,
        limit,
        remaining: limit - entry.count,
        reset: entry.reset,
      }
    },
  }
}

/**
 * Get client identifier from request
 */
function getClientIdentifier(request: NextRequest): string {
  // Try to get real IP from headers (for proxied requests)
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')
  
  // Use the first available IP
  const ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown'
  
  return ip.trim()
}

/**
 * Advanced rate limiter with multiple tiers
 */
export class AdvancedRateLimiter {
  private limits: Map<string, { count: number; reset: number }[]> = new Map()
  private readonly tiers: { limit: number; window: number }[]

  constructor(tiers: { limit: number; window: number }[]) {
    this.tiers = tiers.sort((a, b) => a.window - b.window) // Sort by window size
  }

  async check(request: NextRequest, identifier?: string): Promise<RateLimitResult> {
    const id = identifier || getClientIdentifier(request)
    const now = Date.now()

    // Get or create limits for this identifier
    let userLimits = this.limits.get(id)
    if (!userLimits) {
      userLimits = this.tiers.map(tier => ({
        count: 0,
        reset: now + tier.window,
      }))
      this.limits.set(id, userLimits)
    }

    // Check each tier
    for (let i = 0; i < this.tiers.length; i++) {
      const tier = this.tiers[i]
      const limit = userLimits[i]

      // Reset if window expired
      if (limit.reset < now) {
        limit.count = 0
        limit.reset = now + tier.window
      }

      // Check if this tier is exceeded
      if (limit.count >= tier.limit) {
        return {
          success: false,
          limit: tier.limit,
          remaining: 0,
          reset: limit.reset,
        }
      }
    }

    // Increment all tiers
    userLimits.forEach(limit => limit.count++)

    // Return the most restrictive tier's info
    const mostRestrictive = this.tiers.reduce((prev, curr, index) => {
      const remaining = curr.limit - userLimits![index].count
      return remaining < prev.remaining ? { ...curr, remaining, index } : prev
    }, { limit: this.tiers[0].limit, remaining: this.tiers[0].limit - userLimits[0].count, index: 0 })

    return {
      success: true,
      limit: mostRestrictive.limit,
      remaining: mostRestrictive.remaining,
      reset: userLimits[mostRestrictive.index].reset,
    }
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now()
    for (const [id, limits] of this.limits.entries()) {
      const hasActive = limits.some(limit => limit.reset > now)
      if (!hasActive) {
        this.limits.delete(id)
      }
    }
  }
}

/**
 * Rate limiter with different limits for authenticated vs anonymous users
 */
export class TieredRateLimiter {
  private anonymousLimiter: ReturnType<typeof rateLimit>
  private authenticatedLimiter: ReturnType<typeof rateLimit>
  private premiumLimiter: ReturnType<typeof rateLimit>

  constructor() {
    this.anonymousLimiter = rateLimit({
      interval: 60 * 1000, // 1 minute
      uniqueTokenPerInterval: 1000,
    })

    this.authenticatedLimiter = rateLimit({
      interval: 60 * 1000, // 1 minute
      uniqueTokenPerInterval: 2000,
    })

    this.premiumLimiter = rateLimit({
      interval: 60 * 1000, // 1 minute
      uniqueTokenPerInterval: 5000,
    })
  }

  async check(
    request: NextRequest,
    userTier: 'anonymous' | 'authenticated' | 'premium' = 'anonymous'
  ): Promise<RateLimitResult> {
    const limits = {
      anonymous: 10, // 10 requests per minute
      authenticated: 100, // 100 requests per minute
      premium: 1000, // 1000 requests per minute
    }

    const limiter = {
      anonymous: this.anonymousLimiter,
      authenticated: this.authenticatedLimiter,
      premium: this.premiumLimiter,
    }[userTier]

    return limiter.check(request, limits[userTier])
  }
}

/**
 * Sliding window rate limiter
 */
export class SlidingWindowRateLimiter {
  private windows: Map<string, number[]> = new Map()
  private readonly windowSize: number
  private readonly maxRequests: number

  constructor(windowSize: number, maxRequests: number) {
    this.windowSize = windowSize
    this.maxRequests = maxRequests
  }

  async check(request: NextRequest, identifier?: string): Promise<RateLimitResult> {
    const id = identifier || getClientIdentifier(request)
    const now = Date.now()
    const windowStart = now - this.windowSize

    // Get or create window for this identifier
    let window = this.windows.get(id) || []
    
    // Remove old requests outside the window
    window = window.filter(timestamp => timestamp > windowStart)
    
    // Check if limit exceeded
    if (window.length >= this.maxRequests) {
      const oldestRequest = Math.min(...window)
      const resetTime = oldestRequest + this.windowSize

      return {
        success: false,
        limit: this.maxRequests,
        remaining: 0,
        reset: resetTime,
      }
    }

    // Add current request
    window.push(now)
    this.windows.set(id, window)

    return {
      success: true,
      limit: this.maxRequests,
      remaining: this.maxRequests - window.length,
      reset: now + this.windowSize,
    }
  }

  // Clean up old windows
  cleanup(): void {
    const now = Date.now()
    const cutoff = now - this.windowSize

    for (const [id, window] of this.windows.entries()) {
      const activeRequests = window.filter(timestamp => timestamp > cutoff)
      if (activeRequests.length === 0) {
        this.windows.delete(id)
      } else {
        this.windows.set(id, activeRequests)
      }
    }
  }
}

// Export default rate limiter instance
export const defaultRateLimiter = new TieredRateLimiter()

// Cleanup function to be called periodically
export function cleanupRateLimiters(): void {
  // Clean up token cache
  const now = Date.now()
  for (const [key, value] of tokenCache.entries()) {
    if (value.reset < now) {
      tokenCache.delete(key)
    }
  }
}

// Set up periodic cleanup (every 5 minutes)
if (typeof window === 'undefined') {
  setInterval(cleanupRateLimiters, 5 * 60 * 1000)
}
