import { NextRequest, NextResponse } from 'next/server'
import { MathematicalEngine } from '@/backend/engines/mathematical-engine'
import { rateLimit } from '@/lib/rate-limit'

// Initialize the mathematical engine
const mathEngine = new MathematicalEngine()

// Rate limiting configuration
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500, // Limit each IP to 500 requests per minute
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    try {
      await limiter.check(request, 100, 'CACHE_TOKEN') // 100 requests per minute per IP
    } catch {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { expression, variables = {}, operation = 'evaluate' } = body

    // Validate input
    if (!expression || typeof expression !== 'string') {
      return NextResponse.json(
        { error: 'Expression is required and must be a string' },
        { status: 400 }
      )
    }

    // Sanitize expression to prevent code injection
    const sanitizedExpression = expression.replace(/[^0-9+\-*/().\s\w^√∫∑∏πe,=<>!|&]/g, '')
    
    if (sanitizedExpression !== expression) {
      return NextResponse.json(
        { error: 'Expression contains invalid characters' },
        { status: 400 }
      )
    }

    let result

    switch (operation) {
      case 'evaluate':
        result = mathEngine.evaluate(sanitizedExpression, variables)
        break
      
      case 'solve':
        const variable = body.variable || 'x'
        result = mathEngine.solveEquation(sanitizedExpression, variable)
        break
      
      case 'derivative':
        const derivativeVar = body.variable || 'x'
        result = mathEngine.calculateDerivative(sanitizedExpression, derivativeVar)
        break
      
      case 'integral':
        const integralVar = body.variable || 'x'
        const limits = body.limits
        result = mathEngine.calculateIntegral(sanitizedExpression, integralVar, limits)
        break
      
      case 'matrix':
        const { matrices, matrixOperation } = body
        if (!matrices || !matrixOperation) {
          return NextResponse.json(
            { error: 'Matrices and operation are required for matrix operations' },
            { status: 400 }
          )
        }
        result = mathEngine.matrixOperations(matrixOperation, matrices)
        break
      
      case 'statistics':
        const { data, statisticalOperation } = body
        if (!data || !Array.isArray(data) || !statisticalOperation) {
          return NextResponse.json(
            { error: 'Data array and statistical operation are required' },
            { status: 400 }
          )
        }
        result = mathEngine.statisticalAnalysis(data, statisticalOperation)
        break
      
      default:
        return NextResponse.json(
          { error: `Unknown operation: ${operation}` },
          { status: 400 }
        )
    }

    // Log successful computation for analytics
    console.log(`Mathematical computation: ${operation} - ${sanitizedExpression}`)

    return NextResponse.json({
      success: true,
      operation,
      input: sanitizedExpression,
      result,
      timestamp: new Date().toISOString(),
    })

  } catch (error) {
    console.error('Math evaluation error:', error)
    
    return NextResponse.json(
      {
        error: 'Internal server error during mathematical computation',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  // Health check endpoint
  return NextResponse.json({
    status: 'healthy',
    service: 'mathematical-engine',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  })
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
